<?php

/**
 * 风水玄学水晶饰品销售系统 - 产品数据生成脚本
 * 生成200个水晶饰品产品数据
 */

define('IN_BEIKE', true);
require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

// 加载Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';

// 绑定重要接口到容器
$app->singleton(
    Illuminate\Contracts\Http\Kernel::class,
    App\Http\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);

// 启动应用
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$response = $kernel->handle(
    $request = Illuminate\Http\Request::capture()
);

echo "开始生成风水玄学水晶饰品产品数据...\n";

// 获取语言和货币设置
$languages = DB::table('languages')->where('status', 1)->get();
$defaultLanguage = DB::table('languages')->where('code', 'zh_cn')->first();
if (!$defaultLanguage) {
    $defaultLanguage = $languages->first();
}

$currency = DB::table('currencies')->where('code', 'CNY')->first();
if (!$currency) {
    $currency = DB::table('currencies')->first();
}

// 获取分类数据
$categories = DB::table('categories')
    ->join('category_descriptions', 'categories.id', '=', 'category_descriptions.category_id')
    ->where('category_descriptions.locale', $defaultLanguage->code)
    ->where('categories.parent_id', '>', 0)
    ->select('categories.id', 'category_descriptions.name')
    ->get();

if ($categories->isEmpty()) {
    echo "错误：未找到任何分类。请先运行 feng_shui_categories_setup.php 创建分类。\n";
    exit;
}

// 定义水晶名称和类型，用于生成随机产品
$crystalTypes = [
    '紫水晶', '白水晶', '黄水晶', '粉水晶', '黑曜石', '绿幽灵', '青金石', '虎眼石', '月光石', 
    '茶水晶', '海蓝宝', '摩根石', '碧玺', '玛瑙', '琥珀', '翡翠', '红纹石', '橄榄石', '石榴石',
    '天河石', '拉长石', '发晶', '蓝碧玺', '橙碧玺', '草莓晶', '钛晶', '金发晶', '银发晶', '红发晶',
    '舒俱来', '电气石', '紫龙晶', '蓝铜矿', '孔雀石', '蛋白石', '金发晶'
];

$productTypes = [
    '手链', '项链', '吊坠', '戒指', '耳环', '摆件', '原石', '能量塔', '手串', '脚链', 
    '胸针', '水晶球', '聚宝盆', '印章', '貔貅', '龙龟', '文昌塔', '水晶簇', '聚财树', '能量柱'
];

$effects = [
    '招财', '辟邪', '增运', '健康', '爱情', '智慧', '平衡', '事业', '学业', '平安', 
    '护身', '镇宅', '转运', '开智', '安神', '助眠', '化煞', '人缘', '桃花', '文昌'
];

$qualityLevels = [
    '天然', '精选', '顶级', '珍藏', '臻品', '奢华', '尊贵', '臻藏', '典藏', '极品',
    '稀有', '罕见', '限量', '珍品', '非凡'
];

$sizeOptions = [
    '6mm', '8mm', '10mm', '12mm', '14mm', '16mm', '18mm', '20mm', 
    '小号', '中号', '大号', '标准', '迷你'
];

// 生成商品默认图片URL
$defaultImageUrls = [
    'https://img.alicdn.com/imgextra/i3/2201504856948/O1CN01qLdTU91zGnylGwE3T_!!2201504856948.jpg',
    'https://img.alicdn.com/imgextra/i4/2201504856948/O1CN01YLbEjF1zGnymbVCNv_!!2201504856948.jpg',
    'https://img.alicdn.com/imgextra/i3/2201504856948/O1CN01vZwY4w1zGnyqOMKZI_!!2201504856948.jpg',
    'https://img.alicdn.com/imgextra/i1/2201504856948/O1CN01sdzAJZ1zGnyjWQZDL_!!2201504856948.jpg',
    'https://img.alicdn.com/imgextra/i2/2201504856948/O1CN01vQKwVB1zGnymYN9gr_!!2201504856948.jpg',
    'https://img.alicdn.com/imgextra/i1/2201504856948/O1CN01NUAgaM1zGnypFpcBJ_!!2201504856948.jpg',
    'https://img.alicdn.com/imgextra/i3/2201504856948/O1CN01z5CtXW1zGnyiHXEt9_!!2201504856948.jpg',
    'https://img.alicdn.com/imgextra/i1/2201504856948/O1CN01uMJFNc1zGnyjjlJqy_!!2201504856948.jpg',
    'https://img.alicdn.com/imgextra/i4/2201504856948/O1CN01YFBHyP1zGnymeIFwU_!!2201504856948.jpg',
    'https://img.alicdn.com/imgextra/i3/2201504856948/O1CN01DXRcAw1zGnylGxhVF_!!2201504856948.jpg',
];

// 清除现有产品数据（可选）
if (true) {
    echo "清除现有产品数据...\n";
    DB::statement('SET FOREIGN_KEY_CHECKS=0');
    DB::table('product_descriptions')->truncate();
    DB::table('product_categories')->truncate();
    DB::table('product_attributes')->truncate();
    DB::table('product_skus')->truncate();
    DB::table('products')->truncate();
    DB::statement('SET FOREIGN_KEY_CHECKS=1');
}

// 生成产品详细描述的函数
function generateProductDescription($productName, $crystalType, $effect) {
    $descriptions = [
        // 产品介绍
        "<h3>【{$productName}】</h3>
        <p>灵玉轩精选{$crystalType}，纯手工打造而成。每一颗水晶都经过精心挑选，能量充沛，品相绝佳。</p>
        
        <h3>【材质特点】</h3>
        <p>{$crystalType}被誉为\"能量之石\"，是大自然历经亿万年孕育的珍贵礼物。其晶体结构稳定，能量场强大，是风水玄学中不可或缺的宝石。</p>
        
        <h3>【功效作用】</h3>
        <p>本品主要功效为{$effect}，佩戴后能够：</p>
        <ul>
            <li>调节个人能量场，提升运势</li>
            <li>平衡阴阳五行，化解煞气</li>
            <li>增强个人磁场，吸引正能量</li>
            <li>促进气血循环，改善健康状态</li>
        </ul>
        
        <h3>【使用方法】</h3>
        <p>初次使用请先净化水晶能量，可采用以下方式：</p>
        <ol>
            <li>阳光净化：在清晨阳光下晒2-3小时（注意部分水晶不适合长时间暴晒）</li>
            <li>月光净化：在月圆之夜，将水晶置于月光下一整晚</li>
            <li>流水净化：用清水冲洗15-20分钟</li>
        </ol>
        <p>净化后，请将水晶握在手心冥想3-5分钟，与水晶建立能量连接。</p>
        
        <h3>【风水摆放建议】</h3>
        <p>根据传统风水学理论，本{$crystalType}产品适合摆放在家居或办公室的财位、事业位或健康位，以增强相应位置的能量场。</p>
        
        <h3>【保养方法】</h3>
        <p>请避免与化学品接触，不要沾染香水、化妆品等物质。定期进行能量净化，保持水晶活力。长期不佩戴时，请用紫水晶盒或绒布袋单独存放。</p>
        
        <h3>【注意事项】</h3>
        <p>每个人的磁场不同，佩戴效果可能有所差异。坚持佩戴3-6个月以获得最佳效果。风水物品需要搭配个人努力，才能发挥最大功效。</p>",
        
        // 另一种风格的描述
        "<h2>✨ {$productName} - 灵玉轩臻品 ✨</h2>
        
        <p>来自大自然的馈赠，{$crystalType}以其独特的能量振动频率，成为连接天地之间的神秘媒介。</p>
        
        <h3>🔮 能量特性</h3>
        <p>{$crystalType}蕴含丰富的地脉能量，每一颗都是经过数百万年的地质变化形成的能量宝库。本品经道家大师开光加持，能量场强大纯净。</p>
        
        <h3>💫 核心功效</h3>
        <p>主要功效：{$effect}</p>
        <p>辅助功效：</p>
        <ul>
            <li>净化周围环境的负面能量</li>
            <li>增强个人气场，提升运势</li>
            <li>平衡身心，调和阴阳</li>
            <li>增进福报，化解厄运</li>
        </ul>
        
        <h3>📝 使用指南</h3>
        <p>初次使用建议择吉日开始，可在午时（11:00-13:00）或子时（23:00-1:00）进行简单的开光仪式：</p>
        <ol>
            <li>准备一杯净水，三支香</li>
            <li>点燃香，默念三遍自己的姓名和出生年月</li>
            <li>将水晶置于香前，心中默念愿望</li>
            <li>香燃尽后，即可开始使用</li>
        </ol>
        
        <h3>🏮 风水布局建议</h3>
        <p>根据八宅风水理论，本{$crystalType}适合摆放在：</p>
        <ul>
            <li>东南方位：主财运，有助于招财聚财</li>
            <li>正南方位：主名声，有助于提升声望</li>
            <li>正北方位：主事业，有助于职场发展</li>
        </ul>
        
        <h3>💎 真品保证</h3>
        <p>灵玉轩所有水晶均为天然矿石，无人工合成成分。每件产品都附有鉴定证书，请安心选购。</p>
        
        <h3>🔱 传承千年的智慧</h3>
        <p>自古以来，{$crystalType}就被视为具有神秘力量的宝石，历代帝王将相、修道之人都对其推崇备至。今天，这份来自古老智慧的馈赠，将为您的生活带来和谐与祥瑞。</p>"
    ];
    
    return $descriptions[array_rand($descriptions)];
}

// 生成随机产品名称
function generateProductName($crystalType, $productType, $effect, $qualityLevel) {
    $templates = [
        "{$qualityLevel}{$crystalType}{$productType}",
        "{$qualityLevel}{$effect}{$crystalType}{$productType}",
        "灵玉轩{$qualityLevel}{$crystalType}{$productType}",
        "{$effect}系列{$qualityLevel}{$crystalType}{$productType}",
        "灵玉轩{$effect}{$crystalType}{$productType}",
        "{$crystalType}{$effect}{$productType}",
        "【{$effect}】{$qualityLevel}{$crystalType}{$productType}",
        "福运{$effect}{$crystalType}{$productType}",
        "玄缘{$qualityLevel}{$crystalType}{$productType}",
        "招财开运{$crystalType}{$productType}"
    ];
    
    return $templates[array_rand($templates)];
}

// 开始生成产品
$productCount = 200; // 生成200个产品
$successCount = 0;

for ($i = 0; $i < $productCount; $i++) {
    try {
        // 随机选择产品属性
        $crystalType = $crystalTypes[array_rand($crystalTypes)];
        $productType = $productTypes[array_rand($productTypes)];
        $effect = $effects[array_rand($effects)];
        $qualityLevel = $qualityLevels[array_rand($qualityLevels)];
        
        // 生成产品名称
        $productName = generateProductName($crystalType, $productType, $effect, $qualityLevel);
        
        // 生成产品价格（根据产品类型和水晶类型调整价格区间）
        $basePrice = 0;
        switch ($productType) {
            case '手链':
            case '项链':
                $basePrice = mt_rand(188, 1288);
                break;
            case '摆件':
            case '能量塔':
            case '水晶球':
                $basePrice = mt_rand(288, 2888);
                break;
            case '吊坠':
            case '戒指':
            case '耳环':
                $basePrice = mt_rand(168, 888);
                break;
            case '原石':
                $basePrice = mt_rand(388, 3888);
                break;
            default:
                $basePrice = mt_rand(198, 1988);
        }
        
        // 特殊水晶类型价格调整
        if (in_array($crystalType, ['绿幽灵', '舒俱来', '碧玺', '翡翠'])) {
            $basePrice = $basePrice * mt_rand(15, 25) / 10; // 提高50%-150%
        }
        
        // 确保价格是整数
        $price = round($basePrice / 10) * 10;
        
        // 生成SKU和库存
        $sizes = [];
        $skuCount = mt_rand(1, 4); // 每个产品1-4个规格
        for ($j = 0; $j < $skuCount; $j++) {
            $size = $sizeOptions[array_rand($sizeOptions)];
            if (!in_array($size, $sizes)) {
                $sizes[] = $size;
            }
        }
        
        // 随机选择产品图片
        $imageUrl = $defaultImageUrls[array_rand($defaultImageUrls)];
        
        // 插入产品基本信息
        $productId = DB::table('products')->insertGetId([
            'model' => 'FS-' . strtoupper(substr(md5($productName . time() . $i), 0, 8)),
            'image' => $imageUrl,
            'status' => 1,
            'active' => 1,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        
        // 插入产品描述（多语言支持）
        foreach ($languages as $language) {
            DB::table('product_descriptions')->insert([
                'product_id' => $productId,
                'locale' => $language->code,
                'name' => $productName,
                'description' => generateProductDescription($productName, $crystalType, $effect),
                'meta_title' => $productName,
                'meta_description' => "灵玉轩{$qualityLevel}{$crystalType}{$productType}，主要功效为{$effect}，天然高能量，提升运势，调和五行。",
                'meta_keyword' => "{$crystalType},{$productType},{$effect},风水,玄学,水晶,灵玉轩",
            ]);
        }
        
        // 插入产品分类关联
        // 随机选择1-3个分类关联
        $categoryCount = mt_rand(1, 3);
        $selectedCategories = $categories->random($categoryCount);
        
        foreach ($selectedCategories as $category) {
            DB::table('product_categories')->insert([
                'product_id' => $productId,
                'category_id' => $category->id,
            ]);
        }
        
        // 插入产品SKU
        foreach ($sizes as $size) {
            $skuPrice = $price;
            
            // 较大尺寸价格稍高
            if (in_array($size, ['14mm', '16mm', '18mm', '20mm', '大号'])) {
                $skuPrice += mt_rand(20, 100);
            }
            
            $sku = 'FS-' . strtoupper(substr(md5($productName . $size . time() . $i), 0, 8));
            
            DB::table('product_skus')->insert([
                'product_id' => $productId,
                'sku' => $sku,
                'name' => $size,
                'image' => $imageUrl,
                'model' => $sku,
                'price' => $skuPrice,
                'origin_price' => round($skuPrice * 1.2, -1), // 原价比现价高20%
                'cost_price' => round($skuPrice * 0.4, -1), // 成本价为售价的40%
                'quantity' => mt_rand(10, 100), // 库存10-100
                'is_default' => $size == $sizes[0] ? 1 : 0, // 第一个尺寸为默认
                'status' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
        
        $successCount++;
        if ($successCount % 10 == 0) {
            echo "已生成 {$successCount} 个产品...\n";
        }
    } catch (Exception $e) {
        echo "生成产品时出错: " . $e->getMessage() . "\n";
    }
}

echo "风水玄学水晶饰品产品数据生成完成！\n";
echo "共成功生成 {$successCount} 个产品。\n";
echo "请前往管理后台 -> 商品 -> 商品列表 查看生成的产品。\n";
