// 风水主题分类页面样式

.page-categories {
  background: var(--fengshui-bg-dark);
  min-height: 100vh;
  padding: 30px 0;
}

// 分类头部横幅
.category-banner {
  background: var(--fengshui-gradient-primary);
  padding: 60px 0;
  margin-bottom: 40px;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
      radial-gradient(circle at 20% 50%, rgba(255,215,0,0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255,215,0,0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 80%, rgba(255,215,0,0.1) 0%, transparent 50%);
    animation: mystical-float 15s ease-in-out infinite;
  }
  
  .banner-content {
    position: relative;
    z-index: 1;
    text-align: center;
    
    .category-icon {
      font-size: 4rem;
      color: var(--fengshui-accent);
      margin-bottom: 20px;
      animation: mystical-glow 3s ease-in-out infinite;
    }
    
    .category-title {
      font-size: 3rem;
      color: var(--fengshui-text-light);
      margin-bottom: 15px;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }
    
    .category-description {
      font-size: 1.2rem;
      color: var(--fengshui-text-light);
      opacity: 0.9;
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.6;
    }
  }
}

// 分类导航和筛选
.category-controls {
  background: var(--fengshui-bg-card);
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  border: 1px solid var(--fengshui-border);
  
  .controls-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
    
    .category-nav {
      display: flex;
      gap: 15px;
      flex-wrap: wrap;
      
      .nav-item {
        .nav-link {
          color: var(--fengshui-text-muted);
          text-decoration: none;
          padding: 8px 16px;
          border-radius: 20px;
          transition: all 0.3s ease;
          font-weight: 500;
          
          &:hover,
          &.active {
            background: var(--fengshui-gradient-accent);
            color: var(--fengshui-dark);
          }
        }
      }
    }
    
    .view-controls {
      display: flex;
      align-items: center;
      gap: 15px;
      
      .sort-select {
        background: var(--fengshui-bg-dark);
        border: 1px solid var(--fengshui-border);
        color: var(--fengshui-text-light);
        padding: 8px 12px;
        border-radius: 6px;
        
        &:focus {
          border-color: var(--fengshui-accent);
          outline: none;
        }
      }
      
      .view-toggle {
        display: flex;
        background: var(--fengshui-bg-dark);
        border-radius: 6px;
        overflow: hidden;
        
        .toggle-btn {
          padding: 8px 12px;
          background: transparent;
          border: none;
          color: var(--fengshui-text-muted);
          cursor: pointer;
          transition: all 0.3s ease;
          
          &.active,
          &:hover {
            background: var(--fengshui-accent);
            color: var(--fengshui-dark);
          }
        }
      }
    }
  }
}

// 侧边栏筛选器
.sidebar-filters {
  background: var(--fengshui-bg-card);
  border-radius: 12px;
  padding: 25px;
  border: 1px solid var(--fengshui-border);
  height: fit-content;
  
  .filter-section {
    margin-bottom: 30px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .filter-title {
      color: var(--fengshui-accent);
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      
      i {
        margin-right: 8px;
      }
    }
    
    .filter-options {
      .filter-option {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        
        input[type="checkbox"],
        input[type="radio"] {
          margin-right: 10px;
          accent-color: var(--fengshui-accent);
        }
        
        label {
          color: var(--fengshui-text-light);
          cursor: pointer;
          flex: 1;
          
          &:hover {
            color: var(--fengshui-accent);
          }
        }
        
        .option-count {
          color: var(--fengshui-text-muted);
          font-size: 0.8rem;
        }
      }
    }
    
    .price-range {
      .range-inputs {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
        
        .price-input {
          flex: 1;
          background: var(--fengshui-bg-dark);
          border: 1px solid var(--fengshui-border);
          color: var(--fengshui-text-light);
          padding: 8px 12px;
          border-radius: 6px;
          
          &:focus {
            border-color: var(--fengshui-accent);
            outline: none;
          }
        }
      }
      
      .range-slider {
        .slider {
          -webkit-appearance: none;
          width: 100%;
          height: 6px;
          border-radius: 3px;
          background: var(--fengshui-bg-dark);
          outline: none;
          
          &::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: var(--fengshui-accent);
            cursor: pointer;
          }
          
          &::-moz-range-thumb {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: var(--fengshui-accent);
            cursor: pointer;
            border: none;
          }
        }
      }
    }
  }
  
  .clear-filters {
    width: 100%;
    background: transparent;
    border: 2px solid var(--fengshui-accent);
    color: var(--fengshui-accent);
    padding: 10px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    
    &:hover {
      background: var(--fengshui-accent);
      color: var(--fengshui-dark);
    }
  }
}

// 产品网格布局
.products-grid {
  .grid-container {
    display: grid;
    gap: 25px;
    
    &.grid-2 {
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
    
    &.grid-3 {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    
    &.grid-4 {
      grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    }
    
    &.list-view {
      grid-template-columns: 1fr;
      
      .product-card {
        display: flex;
        align-items: center;
        
        .product-image {
          width: 200px;
          height: 150px;
          flex-shrink: 0;
          margin-right: 20px;
        }
        
        .product-info {
          flex: 1;
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .product-details {
            flex: 1;
          }
          
          .product-actions {
            flex-shrink: 0;
            margin-left: 20px;
          }
        }
      }
    }
  }
}

// 分页导航
.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 40px;
  
  .pagination {
    .page-item {
      margin: 0 3px;
      
      .page-link {
        background: var(--fengshui-bg-card);
        border: 1px solid var(--fengshui-border);
        color: var(--fengshui-text-light);
        padding: 10px 15px;
        border-radius: 8px;
        transition: all 0.3s ease;
        
        &:hover {
          background: var(--fengshui-accent);
          color: var(--fengshui-dark);
          border-color: var(--fengshui-accent);
          transform: translateY(-2px);
        }
      }
      
      &.active .page-link {
        background: var(--fengshui-accent);
        border-color: var(--fengshui-accent);
        color: var(--fengshui-dark);
      }
      
      &.disabled .page-link {
        background: var(--fengshui-bg-dark);
        color: var(--fengshui-text-muted);
        cursor: not-allowed;
        
        &:hover {
          transform: none;
        }
      }
    }
  }
}

// 空状态
.empty-state {
  text-align: center;
  padding: 60px 20px;
  
  .empty-icon {
    font-size: 4rem;
    color: var(--fengshui-text-muted);
    margin-bottom: 20px;
  }
  
  .empty-title {
    font-size: 1.5rem;
    color: var(--fengshui-text-light);
    margin-bottom: 10px;
  }
  
  .empty-description {
    color: var(--fengshui-text-muted);
    margin-bottom: 30px;
  }
  
  .empty-action {
    background: var(--fengshui-gradient-accent);
    color: var(--fengshui-dark);
    border: none;
    padding: 12px 30px;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--fengshui-shadow-gold);
      color: var(--fengshui-dark);
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .category-banner {
    padding: 40px 0;
    
    .banner-content {
      .category-icon {
        font-size: 3rem;
      }
      
      .category-title {
        font-size: 2rem;
      }
      
      .category-description {
        font-size: 1rem;
      }
    }
  }
  
  .category-controls {
    padding: 20px;
    
    .controls-row {
      flex-direction: column;
      align-items: stretch;
      
      .category-nav {
        justify-content: center;
        margin-bottom: 15px;
      }
      
      .view-controls {
        justify-content: center;
      }
    }
  }
  
  .products-grid {
    .grid-container {
      &.grid-2,
      &.grid-3,
      &.grid-4 {
        grid-template-columns: 1fr;
      }
      
      &.list-view {
        .product-card {
          flex-direction: column;
          
          .product-image {
            width: 100%;
            height: 200px;
            margin-right: 0;
            margin-bottom: 15px;
          }
          
          .product-info {
            flex-direction: column;
            align-items: stretch;
            
            .product-actions {
              margin-left: 0;
              margin-top: 15px;
            }
          }
        }
      }
    }
  }
  
  .sidebar-filters {
    margin-bottom: 20px;
  }
}
