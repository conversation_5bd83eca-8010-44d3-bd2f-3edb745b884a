// 风水主题头部样式

.header-wrap {
  background: var(--fengshui-gradient-dark);
  border-bottom: 2px solid var(--fengshui-accent);
  position: sticky;
  top: 0;
  z-index: 1000;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(45, 27, 105, 0.3);
}

// 顶部信息栏
.header-top {
  background: rgba(45, 27, 105, 0.8);
  padding: 8px 0;
  font-size: 0.85rem;
  
  .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .header-info {
    color: var(--fengshui-text-muted);
    
    .info-item {
      margin-right: 20px;
      
      i {
        color: var(--fengshui-accent);
        margin-right: 5px;
      }
    }
  }
  
  .header-actions {
    .action-link {
      color: var(--fengshui-text-light);
      margin-left: 15px;
      transition: color 0.3s ease;
      
      &:hover {
        color: var(--fengshui-accent);
      }
    }
  }
}

// 主导航栏
.header-main {
  padding: 15px 0;
  
  .navbar {
    background: transparent !important;
    padding: 0;
    
    .navbar-brand {
      display: flex;
      align-items: center;
      color: var(--fengshui-accent) !important;
      font-size: 2rem;
      font-weight: bold;
      text-decoration: none;
      
      .logo-icon {
        width: 40px;
        height: 40px;
        margin-right: 10px;
        background: var(--fengshui-gradient-accent);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--fengshui-dark);
        font-size: 1.2rem;
        animation: mystical-glow 3s ease-in-out infinite;
      }
      
      .logo-text {
        background: linear-gradient(45deg, var(--fengshui-accent), #ffb300);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }
    
    .navbar-toggler {
      border: 2px solid var(--fengshui-accent);
      color: var(--fengshui-accent);
      
      &:focus {
        box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
      }
    }
  }
}

// 主导航菜单
.main-nav {
  .nav-link {
    color: var(--fengshui-text-light) !important;
    font-weight: 500;
    padding: 10px 20px !important;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    
    &::before {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 0;
      height: 2px;
      background: var(--fengshui-accent);
      transition: all 0.3s ease;
      transform: translateX(-50%);
    }
    
    &:hover {
      color: var(--fengshui-accent) !important;
      background: rgba(255, 215, 0, 0.1);
      
      &::before {
        width: 80%;
      }
    }
    
    &.active {
      color: var(--fengshui-accent) !important;
      background: rgba(255, 215, 0, 0.15);
      
      &::before {
        width: 80%;
      }
    }
  }
  
  // 下拉菜单
  .dropdown-menu {
    background: var(--fengshui-bg-card);
    border: 1px solid var(--fengshui-border);
    border-radius: 12px;
    box-shadow: var(--fengshui-shadow);
    margin-top: 10px;
    
    .dropdown-item {
      color: var(--fengshui-text-light);
      padding: 10px 20px;
      transition: all 0.3s ease;
      
      &:hover {
        background: var(--fengshui-gradient-primary);
        color: var(--fengshui-text-light);
      }
    }
    
    .dropdown-divider {
      border-color: var(--fengshui-border);
    }
  }
}

// 搜索栏
.header-search {
  position: relative;
  max-width: 400px;
  margin: 0 20px;
  
  .search-form {
    position: relative;
    
    .search-input {
      background: var(--fengshui-bg-card);
      border: 2px solid var(--fengshui-border);
      color: var(--fengshui-text-light);
      border-radius: 25px;
      padding: 12px 50px 12px 20px;
      width: 100%;
      transition: all 0.3s ease;
      
      &:focus {
        border-color: var(--fengshui-accent);
        box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
        background: var(--fengshui-bg-hover);
      }
      
      &::placeholder {
        color: var(--fengshui-text-muted);
      }
    }
    
    .search-btn {
      position: absolute;
      right: 5px;
      top: 50%;
      transform: translateY(-50%);
      background: var(--fengshui-gradient-accent);
      border: none;
      border-radius: 50%;
      width: 35px;
      height: 35px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--fengshui-dark);
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-50%) scale(1.1);
        box-shadow: var(--fengshui-shadow-gold);
      }
    }
  }
}

// 用户操作区域
.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
  
  .action-item {
    position: relative;
    
    .action-link {
      display: flex;
      align-items: center;
      color: var(--fengshui-text-light);
      text-decoration: none;
      padding: 8px 12px;
      border-radius: 8px;
      transition: all 0.3s ease;
      
      &:hover {
        color: var(--fengshui-accent);
        background: rgba(255, 215, 0, 0.1);
      }
      
      .action-icon {
        font-size: 1.2rem;
        margin-right: 5px;
      }
      
      .action-text {
        font-size: 0.9rem;
        font-weight: 500;
      }
      
      .action-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        background: var(--fengshui-accent);
        color: var(--fengshui-dark);
        border-radius: 50%;
        width: 20px;
        height: 20px;
        font-size: 0.7rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        animation: mystical-pulse 2s ease-in-out infinite;
      }
    }
  }
}

// 移动端样式
@media (max-width: 991.98px) {
  .header-top {
    .container {
      flex-direction: column;
      gap: 10px;
    }
    
    .header-info {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      
      .info-item {
        margin: 0 10px;
      }
    }
  }
  
  .header-main {
    .navbar-brand {
      font-size: 1.5rem;
      
      .logo-icon {
        width: 35px;
        height: 35px;
        font-size: 1rem;
      }
    }
  }
  
  .header-search {
    margin: 15px 0;
    max-width: 100%;
  }
  
  .header-actions {
    justify-content: center;
    margin-top: 15px;
  }
  
  .main-nav {
    .navbar-nav {
      text-align: center;
      
      .nav-link {
        margin: 5px 0;
      }
    }
  }
}

// 分类导航栏
.category-nav {
  background: rgba(26, 35, 126, 0.8);
  padding: 10px 0;
  border-bottom: 1px solid var(--fengshui-border);
  
  .category-list {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
    
    .category-link {
      color: var(--fengshui-text-light);
      text-decoration: none;
      padding: 8px 15px;
      border-radius: 20px;
      transition: all 0.3s ease;
      font-size: 0.9rem;
      
      &:hover {
        background: var(--fengshui-accent);
        color: var(--fengshui-dark);
        transform: translateY(-2px);
      }
    }
  }
}

// 滚动时的头部效果
.header-scrolled {
  .header-wrap {
    background: rgba(15, 15, 15, 0.95);
    backdrop-filter: blur(15px);
  }
}
