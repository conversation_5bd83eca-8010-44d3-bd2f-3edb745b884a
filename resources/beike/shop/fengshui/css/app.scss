// 风水玄学水晶饰品主题样式
// 主色调：深紫、深蓝、金色，营造神秘高贵感

// 导入基础样式
@import "../default/css/bootstrap/bootstrap";
@import "../default/css/element-ui";
@import "../default/css/iconfont";

// 风水主题色彩变量
:root {
  // 主色调
  --fengshui-primary: #2d1b69;        // 深紫色
  --fengshui-secondary: #1a237e;      // 深蓝色
  --fengshui-accent: #ffd700;         // 金色
  --fengshui-dark: #0d0d0d;           // 深黑色
  
  // 渐变色
  --fengshui-gradient-primary: linear-gradient(135deg, #2d1b69 0%, #1a237e 100%);
  --fengshui-gradient-accent: linear-gradient(135deg, #ffd700 0%, #ffb300 100%);
  --fengshui-gradient-dark: linear-gradient(135deg, #0d0d0d 0%, #1a1a1a 100%);
  
  // 文字颜色
  --fengshui-text-light: #ffffff;
  --fengshui-text-gold: #ffd700;
  --fengshui-text-muted: #b0b0b0;
  --fengshui-text-dark: #333333;
  
  // 背景色
  --fengshui-bg-dark: #0f0f0f;
  --fengshui-bg-card: #1a1a1a;
  --fengshui-bg-hover: #2a2a2a;
  
  // 边框色
  --fengshui-border: #333333;
  --fengshui-border-light: #444444;
  
  // 阴影
  --fengshui-shadow: 0 4px 20px rgba(45, 27, 105, 0.3);
  --fengshui-shadow-gold: 0 4px 20px rgba(255, 215, 0, 0.2);
}

// 全局样式重置
body {
  background: var(--fengshui-bg-dark);
  color: var(--fengshui-text-light);
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  line-height: 1.6;
}

// 主容器样式
.container, .container-fluid {
  background: transparent;
}

// 卡片样式
.card, .product-card, .category-card {
  background: var(--fengshui-bg-card);
  border: 1px solid var(--fengshui-border);
  border-radius: 12px;
  box-shadow: var(--fengshui-shadow);
  transition: all 0.3s ease;
  
  &:hover {
    background: var(--fengshui-bg-hover);
    box-shadow: var(--fengshui-shadow-gold);
    transform: translateY(-2px);
  }
}

// 按钮样式
.btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &.btn-primary {
    background: var(--fengshui-gradient-primary);
    border: none;
    color: var(--fengshui-text-light);
    
    &:hover {
      background: var(--fengshui-gradient-accent);
      color: var(--fengshui-dark);
      box-shadow: var(--fengshui-shadow-gold);
    }
  }
  
  &.btn-outline-primary {
    border: 2px solid var(--fengshui-accent);
    color: var(--fengshui-accent);
    background: transparent;
    
    &:hover {
      background: var(--fengshui-accent);
      color: var(--fengshui-dark);
    }
  }
}

// 链接样式
a {
  color: var(--fengshui-accent);
  text-decoration: none;
  transition: color 0.3s ease;
  
  &:hover {
    color: var(--fengshui-text-light);
    text-decoration: none;
  }
}

// 标题样式
h1, h2, h3, h4, h5, h6 {
  color: var(--fengshui-text-light);
  font-weight: 600;
  
  &.title-gold {
    color: var(--fengshui-accent);
    text-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
  }
}

// 导航栏样式
.navbar {
  background: var(--fengshui-gradient-dark) !important;
  border-bottom: 2px solid var(--fengshui-accent);
  backdrop-filter: blur(10px);
  
  .navbar-brand {
    color: var(--fengshui-accent) !important;
    font-weight: bold;
    font-size: 1.5rem;
  }
  
  .nav-link {
    color: var(--fengshui-text-light) !important;
    font-weight: 500;
    
    &:hover {
      color: var(--fengshui-accent) !important;
    }
  }
}

// 页脚样式
.footer {
  background: var(--fengshui-gradient-dark);
  border-top: 2px solid var(--fengshui-accent);
  color: var(--fengshui-text-muted);
  
  h5 {
    color: var(--fengshui-accent);
  }
  
  a {
    color: var(--fengshui-text-muted);
    
    &:hover {
      color: var(--fengshui-accent);
    }
  }
}

// 产品网格样式
.product-grid {
  .product-item {
    background: var(--fengshui-bg-card);
    border-radius: 12px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--fengshui-border);
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: var(--fengshui-shadow-gold);
    }
    
    .product-image {
      position: relative;
      overflow: hidden;
      
      img {
        transition: transform 0.3s ease;
      }
      
      &:hover img {
        transform: scale(1.05);
      }
    }
    
    .product-info {
      padding: 1rem;
      
      .product-title {
        color: var(--fengshui-text-light);
        font-weight: 600;
        margin-bottom: 0.5rem;
      }
      
      .product-price {
        color: var(--fengshui-accent);
        font-size: 1.2rem;
        font-weight: bold;
      }
      
      .product-description {
        color: var(--fengshui-text-muted);
        font-size: 0.9rem;
      }
    }
  }
}

// 分类样式
.category-grid {
  .category-item {
    background: var(--fengshui-bg-card);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid var(--fengshui-border);
    
    &:hover {
      background: var(--fengshui-gradient-primary);
      transform: translateY(-3px);
      box-shadow: var(--fengshui-shadow-gold);
    }
    
    .category-icon {
      font-size: 3rem;
      color: var(--fengshui-accent);
      margin-bottom: 1rem;
    }
    
    .category-name {
      color: var(--fengshui-text-light);
      font-weight: 600;
      font-size: 1.1rem;
    }
  }
}

// 表单样式
.form-control {
  background: var(--fengshui-bg-card);
  border: 1px solid var(--fengshui-border);
  color: var(--fengshui-text-light);
  border-radius: 8px;
  
  &:focus {
    background: var(--fengshui-bg-card);
    border-color: var(--fengshui-accent);
    color: var(--fengshui-text-light);
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
  }
  
  &::placeholder {
    color: var(--fengshui-text-muted);
  }
}

// 面包屑导航
.breadcrumb {
  background: transparent;
  
  .breadcrumb-item {
    color: var(--fengshui-text-muted);
    
    &.active {
      color: var(--fengshui-accent);
    }
    
    a {
      color: var(--fengshui-text-light);
    }
  }
}

// 分页样式
.pagination {
  .page-link {
    background: var(--fengshui-bg-card);
    border: 1px solid var(--fengshui-border);
    color: var(--fengshui-text-light);
    
    &:hover {
      background: var(--fengshui-accent);
      color: var(--fengshui-dark);
      border-color: var(--fengshui-accent);
    }
  }
  
  .page-item.active .page-link {
    background: var(--fengshui-accent);
    border-color: var(--fengshui-accent);
    color: var(--fengshui-dark);
  }
}

// 导入其他模块样式
@import "header";
@import "footer";
@import "home";
@import "product";
@import "category";
@import "cart";
@import "mystical-effects";
