// 风水主题购物车样式

.page-cart {
  background: var(--fengshui-bg-dark);
  min-height: 100vh;
  padding: 30px 0;
}

// 购物车主要内容
.cart-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
  
  // 购物车商品列表
  .cart-items {
    background: var(--fengshui-bg-card);
    border-radius: 15px;
    padding: 30px;
    border: 1px solid var(--fengshui-border);
    
    .cart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 25px;
      padding-bottom: 15px;
      border-bottom: 2px solid var(--fengshui-border);
      
      .cart-title {
        font-size: 1.5rem;
        color: var(--fengshui-text-light);
        font-weight: 600;
        display: flex;
        align-items: center;
        
        i {
          color: var(--fengshui-accent);
          margin-right: 10px;
        }
      }
      
      .cart-count {
        color: var(--fengshui-text-muted);
        font-size: 0.9rem;
      }
    }
    
    .cart-item {
      display: flex;
      align-items: center;
      padding: 20px 0;
      border-bottom: 1px solid var(--fengshui-border-light);
      transition: all 0.3s ease;
      
      &:hover {
        background: var(--fengshui-bg-hover);
        margin: 0 -15px;
        padding: 20px 15px;
        border-radius: 8px;
      }
      
      &:last-child {
        border-bottom: none;
      }
      
      .item-image {
        width: 80px;
        height: 80px;
        border-radius: 8px;
        overflow: hidden;
        margin-right: 15px;
        flex-shrink: 0;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      
      .item-details {
        flex: 1;
        margin-right: 15px;
        
        .item-name {
          color: var(--fengshui-text-light);
          font-weight: 600;
          margin-bottom: 5px;
          
          a {
            color: inherit;
            text-decoration: none;
            
            &:hover {
              color: var(--fengshui-accent);
            }
          }
        }
        
        .item-specs {
          color: var(--fengshui-text-muted);
          font-size: 0.9rem;
          margin-bottom: 5px;
        }
        
        .item-price {
          color: var(--fengshui-accent);
          font-weight: 600;
          font-size: 1.1rem;
        }
      }
      
      .item-quantity {
        margin-right: 15px;
        
        .quantity-controls {
          display: flex;
          align-items: center;
          border: 1px solid var(--fengshui-border);
          border-radius: 6px;
          overflow: hidden;
          
          .qty-btn {
            width: 35px;
            height: 35px;
            background: var(--fengshui-bg-dark);
            border: none;
            color: var(--fengshui-text-light);
            cursor: pointer;
            transition: all 0.3s ease;
            
            &:hover {
              background: var(--fengshui-accent);
              color: var(--fengshui-dark);
            }
            
            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }
          }
          
          .qty-input {
            width: 50px;
            height: 35px;
            border: none;
            background: var(--fengshui-bg-card);
            color: var(--fengshui-text-light);
            text-align: center;
            font-weight: 600;
          }
        }
      }
      
      .item-total {
        margin-right: 15px;
        text-align: right;
        
        .total-price {
          color: var(--fengshui-accent);
          font-weight: bold;
          font-size: 1.2rem;
        }
      }
      
      .item-actions {
        .remove-btn {
          width: 35px;
          height: 35px;
          background: transparent;
          border: 1px solid var(--fengshui-border);
          border-radius: 6px;
          color: var(--fengshui-text-muted);
          cursor: pointer;
          transition: all 0.3s ease;
          
          &:hover {
            background: #e74c3c;
            border-color: #e74c3c;
            color: white;
          }
        }
      }
    }
    
    .cart-actions {
      margin-top: 25px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .continue-shopping {
        color: var(--fengshui-accent);
        text-decoration: none;
        font-weight: 500;
        
        &:hover {
          text-decoration: underline;
        }
      }
      
      .clear-cart {
        background: transparent;
        border: 2px solid #e74c3c;
        color: #e74c3c;
        padding: 10px 20px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
        
        &:hover {
          background: #e74c3c;
          color: white;
        }
      }
    }
  }
  
  // 购物车摘要
  .cart-summary {
    background: var(--fengshui-bg-card);
    border-radius: 15px;
    padding: 30px;
    border: 1px solid var(--fengshui-border);
    height: fit-content;
    
    .summary-title {
      font-size: 1.3rem;
      color: var(--fengshui-text-light);
      font-weight: 600;
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      
      i {
        color: var(--fengshui-accent);
        margin-right: 10px;
      }
    }
    
    .summary-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      
      .item-label {
        color: var(--fengshui-text-muted);
      }
      
      .item-value {
        color: var(--fengshui-text-light);
        font-weight: 600;
      }
      
      &.total {
        padding-top: 15px;
        border-top: 2px solid var(--fengshui-border);
        margin-top: 20px;
        
        .item-label {
          color: var(--fengshui-text-light);
          font-size: 1.1rem;
          font-weight: 600;
        }
        
        .item-value {
          color: var(--fengshui-accent);
          font-size: 1.3rem;
          font-weight: bold;
        }
      }
    }
    
    .promo-code {
      margin: 20px 0;
      
      .promo-input {
        display: flex;
        gap: 10px;
        
        input {
          flex: 1;
          background: var(--fengshui-bg-dark);
          border: 1px solid var(--fengshui-border);
          color: var(--fengshui-text-light);
          padding: 10px 12px;
          border-radius: 6px;
          
          &:focus {
            border-color: var(--fengshui-accent);
            outline: none;
          }
          
          &::placeholder {
            color: var(--fengshui-text-muted);
          }
        }
        
        button {
          background: var(--fengshui-gradient-primary);
          color: var(--fengshui-text-light);
          border: none;
          padding: 10px 15px;
          border-radius: 6px;
          font-weight: 600;
          transition: all 0.3s ease;
          
          &:hover {
            background: var(--fengshui-gradient-accent);
            color: var(--fengshui-dark);
          }
        }
      }
    }
    
    .checkout-btn {
      width: 100%;
      background: var(--fengshui-gradient-accent);
      color: var(--fengshui-dark);
      border: none;
      padding: 15px;
      font-size: 1.1rem;
      font-weight: 600;
      border-radius: 8px;
      transition: all 0.3s ease;
      margin-bottom: 15px;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--fengshui-shadow-gold);
      }
    }
    
    .security-info {
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--fengshui-text-muted);
      font-size: 0.9rem;
      
      i {
        color: #27ae60;
        margin-right: 5px;
      }
    }
  }
}

// 空购物车状态
.empty-cart {
  text-align: center;
  padding: 80px 20px;
  background: var(--fengshui-bg-card);
  border-radius: 15px;
  border: 1px solid var(--fengshui-border);
  
  .empty-icon {
    font-size: 5rem;
    color: var(--fengshui-text-muted);
    margin-bottom: 30px;
  }
  
  .empty-title {
    font-size: 2rem;
    color: var(--fengshui-text-light);
    margin-bottom: 15px;
  }
  
  .empty-description {
    color: var(--fengshui-text-muted);
    margin-bottom: 30px;
    font-size: 1.1rem;
  }
  
  .start-shopping {
    background: var(--fengshui-gradient-accent);
    color: var(--fengshui-dark);
    border: none;
    padding: 15px 40px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 8px;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--fengshui-shadow-gold);
      color: var(--fengshui-dark);
    }
  }
}

// 迷你购物车（下拉菜单）
.mini-cart {
  background: var(--fengshui-bg-card);
  border: 1px solid var(--fengshui-border);
  border-radius: 12px;
  box-shadow: var(--fengshui-shadow);
  width: 350px;
  max-height: 400px;
  overflow-y: auto;
  
  .mini-cart-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--fengshui-border);
    
    .cart-title {
      color: var(--fengshui-text-light);
      font-weight: 600;
      margin: 0;
    }
  }
  
  .mini-cart-items {
    max-height: 250px;
    overflow-y: auto;
    
    .mini-cart-item {
      display: flex;
      align-items: center;
      padding: 15px 20px;
      border-bottom: 1px solid var(--fengshui-border-light);
      
      .item-image {
        width: 50px;
        height: 50px;
        border-radius: 6px;
        overflow: hidden;
        margin-right: 12px;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      
      .item-info {
        flex: 1;
        
        .item-name {
          color: var(--fengshui-text-light);
          font-size: 0.9rem;
          font-weight: 500;
          margin-bottom: 3px;
        }
        
        .item-price {
          color: var(--fengshui-accent);
          font-size: 0.8rem;
          font-weight: 600;
        }
      }
      
      .remove-btn {
        background: none;
        border: none;
        color: var(--fengshui-text-muted);
        cursor: pointer;
        padding: 5px;
        
        &:hover {
          color: #e74c3c;
        }
      }
    }
  }
  
  .mini-cart-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--fengshui-border);
    
    .cart-total {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      
      .total-label {
        color: var(--fengshui-text-light);
        font-weight: 600;
      }
      
      .total-amount {
        color: var(--fengshui-accent);
        font-weight: bold;
        font-size: 1.1rem;
      }
    }
    
    .cart-actions {
      display: flex;
      gap: 10px;
      
      .view-cart,
      .checkout {
        flex: 1;
        padding: 8px 12px;
        border-radius: 6px;
        text-decoration: none;
        text-align: center;
        font-size: 0.9rem;
        font-weight: 600;
        transition: all 0.3s ease;
      }
      
      .view-cart {
        background: transparent;
        border: 1px solid var(--fengshui-accent);
        color: var(--fengshui-accent);
        
        &:hover {
          background: var(--fengshui-accent);
          color: var(--fengshui-dark);
        }
      }
      
      .checkout {
        background: var(--fengshui-gradient-accent);
        color: var(--fengshui-dark);
        border: none;
        
        &:hover {
          transform: translateY(-1px);
          box-shadow: var(--fengshui-shadow-gold);
          color: var(--fengshui-dark);
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .cart-content {
    grid-template-columns: 1fr;
    gap: 20px;
    
    .cart-items,
    .cart-summary {
      padding: 20px;
    }
    
    .cart-item {
      flex-direction: column;
      align-items: stretch;
      
      .item-image {
        width: 100%;
        height: 150px;
        margin-right: 0;
        margin-bottom: 15px;
      }
      
      .item-details {
        margin-right: 0;
        margin-bottom: 15px;
      }
      
      .item-quantity,
      .item-total,
      .item-actions {
        margin-right: 0;
        margin-bottom: 10px;
      }
    }
  }
  
  .mini-cart {
    width: 300px;
  }
}
