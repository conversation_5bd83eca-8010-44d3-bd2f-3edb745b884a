// 风水玄学神秘特效样式

// 闪烁动画
@keyframes mystical-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.8), 0 0 30px rgba(255, 215, 0, 0.6);
  }
}

// 旋转动画
@keyframes mystical-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 浮动动画
@keyframes mystical-float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

// 脉冲动画
@keyframes mystical-pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// 渐变背景动画
@keyframes mystical-gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

// 神秘光效类
.mystical-glow {
  animation: mystical-glow 2s ease-in-out infinite;
}

.mystical-rotate {
  animation: mystical-rotate 20s linear infinite;
}

.mystical-float {
  animation: mystical-float 3s ease-in-out infinite;
}

.mystical-pulse {
  animation: mystical-pulse 2s ease-in-out infinite;
}

// 特殊产品卡片效果
.product-mystical {
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 215, 0, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.5s ease;
    opacity: 0;
  }
  
  &:hover::before {
    opacity: 1;
    animation: mystical-rotate 2s linear infinite;
  }
}

// 水晶能量环效果
.crystal-energy-ring {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100px;
    height: 100px;
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: mystical-pulse 3s ease-in-out infinite;
  }
}

// 星空背景效果
.starry-background {
  position: relative;
  background: radial-gradient(ellipse at bottom, #1b2735 0%, #090a0f 100%);
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
      radial-gradient(2px 2px at 20px 30px, #eee, transparent),
      radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
      radial-gradient(1px 1px at 90px 40px, #fff, transparent),
      radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
      radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: mystical-float 20s ease-in-out infinite;
  }
}

// 金色文字发光效果
.text-golden-glow {
  color: var(--fengshui-accent);
  text-shadow: 
    0 0 5px rgba(255, 215, 0, 0.8),
    0 0 10px rgba(255, 215, 0, 0.6),
    0 0 15px rgba(255, 215, 0, 0.4);
  animation: mystical-glow 3s ease-in-out infinite;
}

// 神秘边框效果
.mystical-border {
  position: relative;
  border: 2px solid transparent;
  background: linear-gradient(var(--fengshui-bg-card), var(--fengshui-bg-card)) padding-box,
              linear-gradient(45deg, var(--fengshui-accent), var(--fengshui-primary), var(--fengshui-secondary)) border-box;
  border-radius: 12px;
}

// 水晶折射效果
.crystal-refraction {
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }
  
  &:hover::before {
    left: 100%;
  }
}

// 能量波纹效果
.energy-ripple {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    border: 2px solid rgba(255, 215, 0, 0.6);
    transform: translate(-50%, -50%);
    animation: ripple 2s ease-out infinite;
  }
}

@keyframes ripple {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% {
    width: 200px;
    height: 200px;
    opacity: 0;
  }
}

// 神秘粒子效果
.mystical-particles {
  position: relative;
  
  &::before,
  &::after {
    content: '';
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--fengshui-accent);
    border-radius: 50%;
    animation: particle-float 4s ease-in-out infinite;
  }
  
  &::before {
    top: 20%;
    left: 20%;
    animation-delay: 0s;
  }
  
  &::after {
    top: 60%;
    right: 20%;
    animation-delay: 2s;
  }
}

@keyframes particle-float {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.7;
  }
  25% {
    transform: translateY(-20px) scale(1.2);
    opacity: 1;
  }
  50% {
    transform: translateY(-10px) scale(0.8);
    opacity: 0.5;
  }
  75% {
    transform: translateY(-30px) scale(1.1);
    opacity: 0.8;
  }
}

// 渐变动画背景
.animated-gradient {
  background: linear-gradient(-45deg, #2d1b69, #1a237e, #ffd700, #2d1b69);
  background-size: 400% 400%;
  animation: mystical-gradient 15s ease infinite;
}

// 悬浮卡片效果
.floating-card {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  
  &:hover {
    transform: translateY(-8px) rotateX(5deg);
    box-shadow: 
      0 14px 28px rgba(45, 27, 105, 0.25),
      0 10px 10px rgba(45, 27, 105, 0.22),
      0 0 20px rgba(255, 215, 0, 0.3);
  }
}

// 神秘文字效果
.mystical-text {
  background: linear-gradient(45deg, var(--fengshui-accent), var(--fengshui-text-light), var(--fengshui-accent));
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: mystical-gradient 3s ease infinite;
}

// 水晶光泽效果
.crystal-shine {
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 50%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transform: skewX(-25deg);
    transition: left 0.7s ease;
  }
  
  &:hover::before {
    left: 150%;
  }
}
