// 风水主题页脚样式

.footer {
  background: var(--fengshui-gradient-dark);
  border-top: 2px solid var(--fengshui-accent);
  color: var(--fengshui-text-muted);
  margin-top: 50px;
  position: relative;
  overflow: hidden;
  
  // 背景装饰
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
      radial-gradient(1px 1px at 20px 30px, rgba(255,215,0,0.3), transparent),
      radial-gradient(1px 1px at 40px 70px, rgba(255,215,0,0.2), transparent),
      radial-gradient(1px 1px at 90px 40px, rgba(255,215,0,0.3), transparent);
    background-repeat: repeat;
    background-size: 150px 100px;
    opacity: 0.5;
    pointer-events: none;
  }
  
  .footer-content {
    position: relative;
    z-index: 1;
    padding: 50px 0 30px;
  }
  
  .footer-section {
    margin-bottom: 30px;
    
    .footer-title {
      color: var(--fengshui-accent);
      font-size: 1.2rem;
      font-weight: 600;
      margin-bottom: 20px;
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        bottom: -5px;
        left: 0;
        width: 50px;
        height: 2px;
        background: var(--fengshui-gradient-accent);
      }
    }
    
    .footer-links {
      list-style: none;
      padding: 0;
      
      li {
        margin-bottom: 10px;
        
        a {
          color: var(--fengshui-text-muted);
          text-decoration: none;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          
          &:hover {
            color: var(--fengshui-accent);
            transform: translateX(5px);
          }
          
          i {
            margin-right: 8px;
            width: 16px;
          }
        }
      }
    }
    
    .footer-text {
      line-height: 1.6;
      color: var(--fengshui-text-muted);
    }
  }
  
  // 联系信息样式
  .contact-info {
    .contact-item {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      
      .contact-icon {
        width: 40px;
        height: 40px;
        background: var(--fengshui-gradient-primary);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--fengshui-text-light);
        margin-right: 15px;
        font-size: 1.1rem;
      }
      
      .contact-text {
        flex: 1;
        
        .contact-label {
          color: var(--fengshui-accent);
          font-size: 0.9rem;
          font-weight: 500;
        }
        
        .contact-value {
          color: var(--fengshui-text-light);
          font-size: 1rem;
        }
      }
    }
  }
  
  // 社交媒体链接
  .social-links {
    display: flex;
    gap: 15px;
    margin-top: 20px;
    
    .social-link {
      width: 45px;
      height: 45px;
      background: var(--fengshui-bg-card);
      border: 2px solid var(--fengshui-border);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--fengshui-text-light);
      text-decoration: none;
      transition: all 0.3s ease;
      font-size: 1.2rem;
      
      &:hover {
        background: var(--fengshui-accent);
        color: var(--fengshui-dark);
        border-color: var(--fengshui-accent);
        transform: translateY(-3px);
        box-shadow: var(--fengshui-shadow-gold);
      }
      
      &.facebook:hover { background: #3b5998; border-color: #3b5998; color: white; }
      &.wechat:hover { background: #1aad19; border-color: #1aad19; color: white; }
      &.weibo:hover { background: #e6162d; border-color: #e6162d; color: white; }
      &.qq:hover { background: #12b7f5; border-color: #12b7f5; color: white; }
    }
  }
  
  // 新闻订阅
  .newsletter {
    .newsletter-form {
      display: flex;
      margin-top: 20px;
      
      .newsletter-input {
        flex: 1;
        background: var(--fengshui-bg-card);
        border: 2px solid var(--fengshui-border);
        color: var(--fengshui-text-light);
        padding: 12px 15px;
        border-radius: 8px 0 0 8px;
        
        &:focus {
          border-color: var(--fengshui-accent);
          outline: none;
        }
        
        &::placeholder {
          color: var(--fengshui-text-muted);
        }
      }
      
      .newsletter-btn {
        background: var(--fengshui-gradient-accent);
        border: none;
        color: var(--fengshui-dark);
        padding: 12px 20px;
        border-radius: 0 8px 8px 0;
        font-weight: 600;
        transition: all 0.3s ease;
        
        &:hover {
          transform: scale(1.05);
          box-shadow: var(--fengshui-shadow-gold);
        }
      }
    }
  }
  
  // 页脚底部
  .footer-bottom {
    background: rgba(0, 0, 0, 0.3);
    padding: 20px 0;
    border-top: 1px solid var(--fengshui-border);
    margin-top: 30px;
    
    .footer-bottom-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      gap: 15px;
    }
    
    .copyright {
      color: var(--fengshui-text-muted);
      font-size: 0.9rem;
      
      .brand-name {
        color: var(--fengshui-accent);
        font-weight: 600;
      }
    }
    
    .footer-links-bottom {
      display: flex;
      gap: 20px;
      
      a {
        color: var(--fengshui-text-muted);
        text-decoration: none;
        font-size: 0.9rem;
        transition: color 0.3s ease;
        
        &:hover {
          color: var(--fengshui-accent);
        }
      }
    }
    
    .payment-methods {
      display: flex;
      gap: 10px;
      align-items: center;
      
      .payment-icon {
        width: 40px;
        height: 25px;
        background: var(--fengshui-bg-card);
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        color: var(--fengshui-text-light);
        border: 1px solid var(--fengshui-border);
      }
    }
  }
  
  // 返回顶部按钮
  .back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--fengshui-gradient-accent);
    border: none;
    border-radius: 50%;
    color: var(--fengshui-dark);
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: var(--fengshui-shadow);
    opacity: 0;
    visibility: hidden;
    
    &.show {
      opacity: 1;
      visibility: visible;
    }
    
    &:hover {
      transform: translateY(-3px) scale(1.1);
      box-shadow: var(--fengshui-shadow-gold);
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .footer {
    .footer-content {
      padding: 30px 0 20px;
    }
    
    .footer-section {
      text-align: center;
      margin-bottom: 25px;
    }
    
    .contact-info {
      .contact-item {
        justify-content: center;
      }
    }
    
    .social-links {
      justify-content: center;
    }
    
    .newsletter {
      .newsletter-form {
        flex-direction: column;
        gap: 10px;
        
        .newsletter-input,
        .newsletter-btn {
          border-radius: 8px;
        }
      }
    }
    
    .footer-bottom {
      .footer-bottom-content {
        flex-direction: column;
        text-align: center;
      }
      
      .footer-links-bottom {
        flex-wrap: wrap;
        justify-content: center;
      }
    }
    
    .back-to-top {
      bottom: 20px;
      right: 20px;
      width: 45px;
      height: 45px;
    }
  }
}
