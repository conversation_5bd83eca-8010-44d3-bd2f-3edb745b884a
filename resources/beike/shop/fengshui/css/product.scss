// 风水主题产品页面样式

.page-product {
  background: var(--fengshui-bg-dark);
  min-height: 100vh;
  padding: 30px 0;
}

// 产品详情主要区域
.product-detail {
  background: var(--fengshui-bg-card);
  border-radius: 15px;
  padding: 40px;
  margin-bottom: 40px;
  border: 1px solid var(--fengshui-border);
  box-shadow: var(--fengshui-shadow);
  
  .product-main {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
    align-items: start;
    
    // 产品图片区域
    .product-images {
      .main-image {
        position: relative;
        margin-bottom: 20px;
        border-radius: 12px;
        overflow: hidden;
        background: var(--fengshui-bg-dark);
        
        img {
          width: 100%;
          height: 500px;
          object-fit: cover;
          transition: transform 0.3s ease;
        }
        
        &:hover img {
          transform: scale(1.05);
        }
        
        .image-badge {
          position: absolute;
          top: 20px;
          left: 20px;
          background: var(--fengshui-gradient-accent);
          color: var(--fengshui-dark);
          padding: 8px 15px;
          border-radius: 20px;
          font-size: 0.9rem;
          font-weight: 600;
        }
        
        .zoom-icon {
          position: absolute;
          top: 20px;
          right: 20px;
          width: 40px;
          height: 40px;
          background: rgba(0, 0, 0, 0.7);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          cursor: pointer;
          transition: all 0.3s ease;
          
          &:hover {
            background: var(--fengshui-accent);
            color: var(--fengshui-dark);
          }
        }
      }
      
      .thumbnail-images {
        display: flex;
        gap: 15px;
        overflow-x: auto;
        padding: 10px 0;
        
        .thumbnail {
          flex-shrink: 0;
          width: 80px;
          height: 80px;
          border-radius: 8px;
          overflow: hidden;
          cursor: pointer;
          border: 2px solid transparent;
          transition: all 0.3s ease;
          
          &.active,
          &:hover {
            border-color: var(--fengshui-accent);
          }
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
    }
    
    // 产品信息区域
    .product-info {
      .product-category {
        color: var(--fengshui-accent);
        font-size: 0.9rem;
        font-weight: 500;
        text-transform: uppercase;
        margin-bottom: 10px;
      }
      
      .product-title {
        font-size: 2.2rem;
        font-weight: bold;
        color: var(--fengshui-text-light);
        margin-bottom: 15px;
        line-height: 1.3;
      }
      
      .product-rating {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 20px;
        
        .stars {
          color: var(--fengshui-accent);
          font-size: 1.1rem;
        }
        
        .rating-text {
          color: var(--fengshui-text-muted);
          font-size: 0.9rem;
        }
        
        .review-count {
          color: var(--fengshui-accent);
          text-decoration: none;
          
          &:hover {
            text-decoration: underline;
          }
        }
      }
      
      .product-price {
        margin-bottom: 25px;
        
        .current-price {
          font-size: 2.5rem;
          font-weight: bold;
          color: var(--fengshui-accent);
          margin-right: 15px;
        }
        
        .original-price {
          font-size: 1.5rem;
          color: var(--fengshui-text-muted);
          text-decoration: line-through;
        }
        
        .discount-badge {
          background: #e74c3c;
          color: white;
          padding: 5px 10px;
          border-radius: 15px;
          font-size: 0.8rem;
          font-weight: 600;
          margin-left: 10px;
        }
      }
      
      .product-description {
        color: var(--fengshui-text-muted);
        line-height: 1.6;
        margin-bottom: 30px;
        font-size: 1rem;
      }
      
      // 产品功效说明
      .product-effects {
        background: var(--fengshui-gradient-primary);
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 25px;
        
        .effects-title {
          color: var(--fengshui-accent);
          font-size: 1.1rem;
          font-weight: 600;
          margin-bottom: 15px;
          display: flex;
          align-items: center;
          
          i {
            margin-right: 8px;
            font-size: 1.2rem;
          }
        }
        
        .effects-list {
          list-style: none;
          padding: 0;
          
          li {
            color: var(--fengshui-text-light);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            
            &::before {
              content: '✦';
              color: var(--fengshui-accent);
              margin-right: 10px;
              font-size: 1.1rem;
            }
          }
        }
      }
      
      // 产品选项
      .product-options {
        margin-bottom: 30px;
        
        .option-group {
          margin-bottom: 20px;
          
          .option-label {
            color: var(--fengshui-text-light);
            font-weight: 600;
            margin-bottom: 10px;
            display: block;
          }
          
          .option-values {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            
            .option-value {
              padding: 8px 15px;
              border: 2px solid var(--fengshui-border);
              border-radius: 8px;
              background: var(--fengshui-bg-dark);
              color: var(--fengshui-text-light);
              cursor: pointer;
              transition: all 0.3s ease;
              
              &:hover,
              &.selected {
                border-color: var(--fengshui-accent);
                background: var(--fengshui-accent);
                color: var(--fengshui-dark);
              }
            }
          }
        }
        
        .quantity-selector {
          display: flex;
          align-items: center;
          gap: 15px;
          
          .quantity-label {
            color: var(--fengshui-text-light);
            font-weight: 600;
          }
          
          .quantity-controls {
            display: flex;
            align-items: center;
            border: 2px solid var(--fengshui-border);
            border-radius: 8px;
            overflow: hidden;
            
            .qty-btn {
              width: 40px;
              height: 40px;
              background: var(--fengshui-bg-dark);
              border: none;
              color: var(--fengshui-text-light);
              cursor: pointer;
              transition: all 0.3s ease;
              
              &:hover {
                background: var(--fengshui-accent);
                color: var(--fengshui-dark);
              }
            }
            
            .qty-input {
              width: 60px;
              height: 40px;
              border: none;
              background: var(--fengshui-bg-card);
              color: var(--fengshui-text-light);
              text-align: center;
              font-weight: 600;
            }
          }
        }
      }
      
      // 购买按钮区域
      .purchase-actions {
        display: flex;
        gap: 15px;
        margin-bottom: 30px;
        
        .add-to-cart {
          flex: 1;
          background: var(--fengshui-gradient-accent);
          color: var(--fengshui-dark);
          border: none;
          padding: 15px 30px;
          font-size: 1.1rem;
          font-weight: 600;
          border-radius: 8px;
          transition: all 0.3s ease;
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: var(--fengshui-shadow-gold);
          }
        }
        
        .buy-now {
          flex: 1;
          background: var(--fengshui-gradient-primary);
          color: var(--fengshui-text-light);
          border: none;
          padding: 15px 30px;
          font-size: 1.1rem;
          font-weight: 600;
          border-radius: 8px;
          transition: all 0.3s ease;
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: var(--fengshui-shadow);
          }
        }
        
        .wishlist-btn {
          width: 50px;
          height: 50px;
          background: var(--fengshui-bg-dark);
          border: 2px solid var(--fengshui-border);
          border-radius: 8px;
          color: var(--fengshui-text-light);
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          
          &:hover,
          &.active {
            border-color: var(--fengshui-accent);
            color: var(--fengshui-accent);
          }
        }
      }
      
      // 产品特性
      .product-features {
        .feature-item {
          display: flex;
          align-items: center;
          margin-bottom: 15px;
          
          .feature-icon {
            width: 40px;
            height: 40px;
            background: var(--fengshui-gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--fengshui-text-light);
            margin-right: 15px;
          }
          
          .feature-text {
            color: var(--fengshui-text-light);
            font-size: 0.9rem;
          }
        }
      }
    }
  }
}

// 产品详情标签页
.product-tabs {
  background: var(--fengshui-bg-card);
  border-radius: 15px;
  padding: 30px;
  margin-bottom: 40px;
  border: 1px solid var(--fengshui-border);
  
  .nav-tabs {
    border-bottom: 2px solid var(--fengshui-border);
    margin-bottom: 30px;
    
    .nav-link {
      background: transparent;
      border: none;
      color: var(--fengshui-text-muted);
      padding: 15px 25px;
      font-weight: 600;
      border-radius: 0;
      border-bottom: 3px solid transparent;
      transition: all 0.3s ease;
      
      &:hover,
      &.active {
        color: var(--fengshui-accent);
        border-bottom-color: var(--fengshui-accent);
        background: transparent;
      }
    }
  }
  
  .tab-content {
    color: var(--fengshui-text-light);
    line-height: 1.6;
    
    h3, h4, h5 {
      color: var(--fengshui-accent);
      margin-bottom: 15px;
    }
    
    p {
      margin-bottom: 15px;
    }
    
    ul {
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        
        &::marker {
          color: var(--fengshui-accent);
        }
      }
    }
  }
}

// 相关产品
.related-products {
  .section-title {
    text-align: center;
    margin-bottom: 40px;
    
    h3 {
      font-size: 2rem;
      color: var(--fengshui-text-light);
      margin-bottom: 10px;
    }
    
    .section-subtitle {
      color: var(--fengshui-text-muted);
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .product-detail {
    padding: 20px;
    
    .product-main {
      grid-template-columns: 1fr;
      gap: 30px;
      
      .product-images {
        .main-image {
          img {
            height: 300px;
          }
        }
      }
      
      .product-info {
        .product-title {
          font-size: 1.8rem;
        }
        
        .product-price {
          .current-price {
            font-size: 2rem;
          }
        }
        
        .purchase-actions {
          flex-direction: column;
          
          .add-to-cart,
          .buy-now {
            flex: none;
          }
        }
      }
    }
  }
  
  .product-tabs {
    padding: 20px;
    
    .nav-tabs {
      .nav-link {
        padding: 10px 15px;
        font-size: 0.9rem;
      }
    }
  }
}
