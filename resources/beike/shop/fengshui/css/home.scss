// 风水主题首页样式

.page-home {
  background: var(--fengshui-bg-dark);
  min-height: 100vh;
}

// 轮播图样式
.hero-carousel {
  position: relative;
  margin-bottom: 50px;
  
  .carousel-item {
    height: 500px;
    background-size: cover;
    background-position: center;
    position: relative;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(45deg, rgba(45, 27, 105, 0.7), rgba(26, 35, 126, 0.5));
    }
    
    .carousel-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      color: var(--fengshui-text-light);
      z-index: 2;
      
      .hero-title {
        font-size: 3rem;
        font-weight: bold;
        margin-bottom: 20px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        animation: mystical-float 3s ease-in-out infinite;
        
        .highlight {
          color: var(--fengshui-accent);
          text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }
      }
      
      .hero-subtitle {
        font-size: 1.2rem;
        margin-bottom: 30px;
        color: var(--fengshui-text-muted);
      }
      
      .hero-btn {
        background: var(--fengshui-gradient-accent);
        color: var(--fengshui-dark);
        border: none;
        padding: 15px 40px;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 30px;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        
        &:hover {
          transform: translateY(-3px) scale(1.05);
          box-shadow: var(--fengshui-shadow-gold);
          color: var(--fengshui-dark);
        }
      }
    }
  }
  
  .carousel-indicators {
    bottom: 20px;
    
    button {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: rgba(255, 215, 0, 0.5);
      border: 2px solid var(--fengshui-accent);
      
      &.active {
        background: var(--fengshui-accent);
      }
    }
  }
  
  .carousel-control-prev,
  .carousel-control-next {
    width: 60px;
    height: 60px;
    background: rgba(45, 27, 105, 0.8);
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    
    &:hover {
      background: var(--fengshui-accent);
      
      .carousel-control-prev-icon,
      .carousel-control-next-icon {
        filter: brightness(0);
      }
    }
  }
  
  .carousel-control-prev {
    left: 30px;
  }
  
  .carousel-control-next {
    right: 30px;
  }
}

// 特色分类区域
.featured-categories {
  margin-bottom: 60px;
  
  .section-title {
    text-align: center;
    margin-bottom: 40px;
    
    h2 {
      font-size: 2.5rem;
      color: var(--fengshui-text-light);
      margin-bottom: 15px;
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 3px;
        background: var(--fengshui-gradient-accent);
      }
    }
    
    .section-subtitle {
      color: var(--fengshui-text-muted);
      font-size: 1.1rem;
    }
  }
  
  .category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    
    .category-card {
      background: var(--fengshui-bg-card);
      border-radius: 15px;
      padding: 30px 20px;
      text-align: center;
      transition: all 0.3s ease;
      border: 1px solid var(--fengshui-border);
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: var(--fengshui-gradient-primary);
        transition: left 0.5s ease;
        z-index: 1;
      }
      
      &:hover {
        transform: translateY(-10px);
        box-shadow: var(--fengshui-shadow-gold);
        
        &::before {
          left: 0;
        }
        
        .category-content {
          position: relative;
          z-index: 2;
        }
      }
      
      .category-icon {
        font-size: 3.5rem;
        color: var(--fengshui-accent);
        margin-bottom: 20px;
        animation: mystical-glow 3s ease-in-out infinite;
      }
      
      .category-name {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--fengshui-text-light);
        margin-bottom: 10px;
      }
      
      .category-description {
        color: var(--fengshui-text-muted);
        font-size: 0.9rem;
        line-height: 1.5;
      }
      
      .category-link {
        text-decoration: none;
        color: inherit;
      }
    }
  }
}

// 热门产品区域
.featured-products {
  margin-bottom: 60px;
  
  .product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    
    .product-card {
      background: var(--fengshui-bg-card);
      border-radius: 15px;
      overflow: hidden;
      transition: all 0.3s ease;
      border: 1px solid var(--fengshui-border);
      
      &:hover {
        transform: translateY(-8px);
        box-shadow: var(--fengshui-shadow-gold);
      }
      
      .product-image {
        position: relative;
        height: 250px;
        overflow: hidden;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease;
        }
        
        &:hover img {
          transform: scale(1.1);
        }
        
        .product-badge {
          position: absolute;
          top: 15px;
          left: 15px;
          background: var(--fengshui-gradient-accent);
          color: var(--fengshui-dark);
          padding: 5px 12px;
          border-radius: 20px;
          font-size: 0.8rem;
          font-weight: 600;
        }
        
        .product-actions {
          position: absolute;
          top: 15px;
          right: 15px;
          display: flex;
          flex-direction: column;
          gap: 10px;
          opacity: 0;
          transition: opacity 0.3s ease;
          
          .action-btn {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--fengshui-dark);
            transition: all 0.3s ease;
            
            &:hover {
              background: var(--fengshui-accent);
              transform: scale(1.1);
            }
          }
        }
        
        &:hover .product-actions {
          opacity: 1;
        }
      }
      
      .product-info {
        padding: 20px;
        
        .product-category {
          color: var(--fengshui-accent);
          font-size: 0.8rem;
          font-weight: 500;
          text-transform: uppercase;
          margin-bottom: 8px;
        }
        
        .product-title {
          font-size: 1.1rem;
          font-weight: 600;
          color: var(--fengshui-text-light);
          margin-bottom: 10px;
          line-height: 1.4;
          
          a {
            color: inherit;
            text-decoration: none;
            
            &:hover {
              color: var(--fengshui-accent);
            }
          }
        }
        
        .product-description {
          color: var(--fengshui-text-muted);
          font-size: 0.9rem;
          line-height: 1.5;
          margin-bottom: 15px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        .product-price {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 15px;
          
          .current-price {
            font-size: 1.3rem;
            font-weight: bold;
            color: var(--fengshui-accent);
          }
          
          .original-price {
            font-size: 1rem;
            color: var(--fengshui-text-muted);
            text-decoration: line-through;
          }
        }
        
        .product-rating {
          display: flex;
          align-items: center;
          gap: 5px;
          margin-bottom: 15px;
          
          .stars {
            color: var(--fengshui-accent);
          }
          
          .rating-text {
            color: var(--fengshui-text-muted);
            font-size: 0.8rem;
          }
        }
        
        .add-to-cart {
          width: 100%;
          background: var(--fengshui-gradient-primary);
          color: var(--fengshui-text-light);
          border: none;
          padding: 12px;
          border-radius: 8px;
          font-weight: 600;
          transition: all 0.3s ease;
          
          &:hover {
            background: var(--fengshui-gradient-accent);
            color: var(--fengshui-dark);
            transform: translateY(-2px);
          }
        }
      }
    }
  }
}

// 品牌故事区域
.brand-story {
  background: var(--fengshui-gradient-primary);
  padding: 80px 0;
  margin-bottom: 60px;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,215,0,0.1)"/></svg>');
    background-size: 50px 50px;
    animation: mystical-float 20s ease-in-out infinite;
  }
  
  .story-content {
    position: relative;
    z-index: 1;
    text-align: center;
    
    .story-title {
      font-size: 2.5rem;
      color: var(--fengshui-text-light);
      margin-bottom: 30px;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }
    
    .story-text {
      font-size: 1.1rem;
      color: var(--fengshui-text-light);
      line-height: 1.8;
      max-width: 800px;
      margin: 0 auto 40px;
      opacity: 0.9;
    }
    
    .story-btn {
      background: var(--fengshui-gradient-accent);
      color: var(--fengshui-dark);
      border: none;
      padding: 15px 40px;
      font-size: 1.1rem;
      font-weight: 600;
      border-radius: 30px;
      transition: all 0.3s ease;
      text-decoration: none;
      display: inline-block;
      
      &:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: var(--fengshui-shadow-gold);
        color: var(--fengshui-dark);
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .hero-carousel {
    .carousel-item {
      height: 400px;
      
      .carousel-content {
        .hero-title {
          font-size: 2rem;
        }
        
        .hero-subtitle {
          font-size: 1rem;
        }
      }
    }
    
    .carousel-control-prev,
    .carousel-control-next {
      width: 50px;
      height: 50px;
    }
    
    .carousel-control-prev {
      left: 15px;
    }
    
    .carousel-control-next {
      right: 15px;
    }
  }
  
  .featured-categories {
    .section-title h2 {
      font-size: 2rem;
    }
    
    .category-grid {
      grid-template-columns: 1fr;
      gap: 20px;
    }
  }
  
  .featured-products {
    .product-grid {
      grid-template-columns: 1fr;
      gap: 20px;
    }
  }
  
  .brand-story {
    padding: 50px 0;
    
    .story-content {
      .story-title {
        font-size: 2rem;
      }
      
      .story-text {
        font-size: 1rem;
      }
    }
  }
}
