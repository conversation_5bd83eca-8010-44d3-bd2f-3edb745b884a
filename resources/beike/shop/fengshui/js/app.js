// 风水主题JavaScript功能

// 导入基础功能
require('../default/js/common');
require('../default/js/header');
require('../default/js/footer');
require('../default/js/product');

// 风水主题特有功能
$(document).ready(function() {
    
    // 初始化神秘特效
    initMysticalEffects();
    
    // 初始化滚动效果
    initScrollEffects();
    
    // 初始化产品交互
    initProductInteractions();
    
    // 初始化购物车功能
    initCartFunctions();
    
    // 初始化搜索功能
    initSearchFunctions();
    
    // 初始化响应式功能
    initResponsiveFunctions();
});

// 神秘特效初始化
function initMysticalEffects() {
    // 鼠标跟随粒子效果
    createMouseParticles();
    
    // 随机闪烁效果
    createRandomGlow();
    
    // 水晶能量环效果
    createEnergyRings();
    
    // 星空背景动画
    animateStarryBackground();
}

// 鼠标跟随粒子效果
function createMouseParticles() {
    let particles = [];
    const maxParticles = 15;
    
    $(document).mousemove(function(e) {
        if (particles.length < maxParticles) {
            const particle = $('<div class="mouse-particle"></div>');
            particle.css({
                position: 'fixed',
                left: e.clientX + 'px',
                top: e.clientY + 'px',
                width: '4px',
                height: '4px',
                background: 'var(--fengshui-accent)',
                borderRadius: '50%',
                pointerEvents: 'none',
                zIndex: 9999,
                opacity: 0.8
            });
            
            $('body').append(particle);
            particles.push(particle);
            
            // 粒子动画
            particle.animate({
                opacity: 0,
                top: (e.clientY - 50) + 'px'
            }, 1000, function() {
                particle.remove();
                particles = particles.filter(p => p !== particle);
            });
        }
    });
}

// 随机闪烁效果
function createRandomGlow() {
    setInterval(function() {
        $('.mystical-glow').each(function() {
            const element = $(this);
            const shouldGlow = Math.random() > 0.7;
            
            if (shouldGlow) {
                element.addClass('active-glow');
                setTimeout(function() {
                    element.removeClass('active-glow');
                }, 1500);
            }
        });
    }, 3000);
}

// 水晶能量环效果
function createEnergyRings() {
    $('.crystal-energy-ring').each(function() {
        const element = $(this);
        setInterval(function() {
            const ring = $('<div class="energy-ring"></div>');
            ring.css({
                position: 'absolute',
                top: '50%',
                left: '50%',
                width: '10px',
                height: '10px',
                border: '2px solid rgba(255, 215, 0, 0.6)',
                borderRadius: '50%',
                transform: 'translate(-50%, -50%)',
                pointerEvents: 'none'
            });
            
            element.append(ring);
            
            ring.animate({
                width: '200px',
                height: '200px',
                opacity: 0
            }, 2000, function() {
                ring.remove();
            });
        }, 4000);
    });
}

// 星空背景动画
function animateStarryBackground() {
    $('.starry-background').each(function() {
        const element = $(this);
        let stars = '';
        
        for (let i = 0; i < 50; i++) {
            const x = Math.random() * 100;
            const y = Math.random() * 100;
            const size = Math.random() * 2 + 1;
            const duration = Math.random() * 3 + 2;
            
            stars += `<div class="star" style="
                left: ${x}%;
                top: ${y}%;
                width: ${size}px;
                height: ${size}px;
                animation: twinkle ${duration}s ease-in-out infinite;
            "></div>`;
        }
        
        element.append(stars);
    });
}

// 滚动效果初始化
function initScrollEffects() {
    // 返回顶部按钮
    const backToTop = $('.back-to-top');
    
    $(window).scroll(function() {
        if ($(this).scrollTop() > 300) {
            backToTop.addClass('show');
        } else {
            backToTop.removeClass('show');
        }
        
        // 头部滚动效果
        if ($(this).scrollTop() > 100) {
            $('.header-wrap').addClass('header-scrolled');
        } else {
            $('.header-wrap').removeClass('header-scrolled');
        }
    });
    
    // 返回顶部点击事件
    backToTop.click(function() {
        $('html, body').animate({scrollTop: 0}, 800);
        return false;
    });
    
    // 滚动视差效果
    initParallaxEffect();
}

// 视差效果
function initParallaxEffect() {
    $(window).scroll(function() {
        const scrolled = $(this).scrollTop();
        const parallax = $('.parallax-element');
        const speed = 0.5;
        
        parallax.each(function() {
            const yPos = -(scrolled * speed);
            $(this).css('transform', `translateY(${yPos}px)`);
        });
    });
}

// 产品交互初始化
function initProductInteractions() {
    // 产品图片切换
    $('.thumbnail').click(function() {
        const newSrc = $(this).find('img').attr('src');
        $('.main-image img').attr('src', newSrc);
        $('.thumbnail').removeClass('active');
        $(this).addClass('active');
    });
    
    // 数量选择器
    $('.qty-btn').click(function() {
        const input = $(this).siblings('.qty-input');
        const isIncrease = $(this).hasClass('qty-increase');
        let currentVal = parseInt(input.val()) || 1;
        
        if (isIncrease) {
            currentVal++;
        } else {
            currentVal = Math.max(1, currentVal - 1);
        }
        
        input.val(currentVal);
        updateProductTotal();
    });
    
    // 产品选项选择
    $('.option-value').click(function() {
        $(this).siblings().removeClass('selected');
        $(this).addClass('selected');
        updateProductPrice();
    });
    
    // 愿望清单切换
    $('.wishlist-btn').click(function() {
        $(this).toggleClass('active');
        const isActive = $(this).hasClass('active');
        
        if (isActive) {
            showNotification('已添加到愿望清单', 'success');
        } else {
            showNotification('已从愿望清单移除', 'info');
        }
    });
}

// 更新产品总价
function updateProductTotal() {
    const price = parseFloat($('.current-price').text().replace(/[^\d.]/g, ''));
    const quantity = parseInt($('.qty-input').val()) || 1;
    const total = price * quantity;
    
    $('.product-total').text('¥' + total.toFixed(2));
}

// 更新产品价格
function updateProductPrice() {
    // 根据选择的选项更新价格
    const selectedOptions = $('.option-value.selected');
    let basePrice = parseFloat($('.current-price').data('base-price')) || 0;
    
    selectedOptions.each(function() {
        const optionPrice = parseFloat($(this).data('price')) || 0;
        basePrice += optionPrice;
    });
    
    $('.current-price').text('¥' + basePrice.toFixed(2));
    updateProductTotal();
}

// 购物车功能初始化
function initCartFunctions() {
    // 添加到购物车
    $('.add-to-cart').click(function() {
        const productId = $(this).data('product-id');
        const quantity = $('.qty-input').val() || 1;
        
        addToCart(productId, quantity);
    });
    
    // 立即购买
    $('.buy-now').click(function() {
        const productId = $(this).data('product-id');
        const quantity = $('.qty-input').val() || 1;
        
        buyNow(productId, quantity);
    });
    
    // 购物车数量更新
    $('.cart-qty-btn').click(function() {
        const input = $(this).siblings('.qty-input');
        const isIncrease = $(this).hasClass('qty-increase');
        let currentVal = parseInt(input.val()) || 1;
        
        if (isIncrease) {
            currentVal++;
        } else {
            currentVal = Math.max(1, currentVal - 1);
        }
        
        input.val(currentVal);
        updateCartItem($(this).closest('.cart-item'));
    });
    
    // 移除购物车商品
    $('.remove-btn').click(function() {
        const cartItem = $(this).closest('.cart-item');
        removeCartItem(cartItem);
    });
}

// 添加到购物车
function addToCart(productId, quantity) {
    // 显示加载动画
    showLoading();
    
    // 模拟API调用
    setTimeout(function() {
        hideLoading();
        showNotification('商品已添加到购物车', 'success');
        updateCartCount();
        
        // 添加飞入动画
        createFlyToCartAnimation();
    }, 1000);
}

// 立即购买
function buyNow(productId, quantity) {
    showLoading();
    
    setTimeout(function() {
        hideLoading();
        window.location.href = '/checkout';
    }, 1000);
}

// 更新购物车商品
function updateCartItem(cartItem) {
    const quantity = cartItem.find('.qty-input').val();
    const price = parseFloat(cartItem.find('.item-price').text().replace(/[^\d.]/g, ''));
    const total = price * quantity;
    
    cartItem.find('.total-price').text('¥' + total.toFixed(2));
    updateCartSummary();
}

// 移除购物车商品
function removeCartItem(cartItem) {
    cartItem.fadeOut(300, function() {
        $(this).remove();
        updateCartSummary();
        
        if ($('.cart-item').length === 0) {
            showEmptyCart();
        }
    });
}

// 更新购物车摘要
function updateCartSummary() {
    let subtotal = 0;
    
    $('.cart-item').each(function() {
        const total = parseFloat($(this).find('.total-price').text().replace(/[^\d.]/g, ''));
        subtotal += total;
    });
    
    const shipping = 10; // 固定运费
    const tax = subtotal * 0.1; // 10%税费
    const total = subtotal + shipping + tax;
    
    $('.subtotal .item-value').text('¥' + subtotal.toFixed(2));
    $('.shipping .item-value').text('¥' + shipping.toFixed(2));
    $('.tax .item-value').text('¥' + tax.toFixed(2));
    $('.total .item-value').text('¥' + total.toFixed(2));
}

// 飞入购物车动画
function createFlyToCartAnimation() {
    const productImage = $('.main-image img');
    const cartIcon = $('.cart-icon');
    
    if (productImage.length && cartIcon.length) {
        const flyImage = productImage.clone();
        const startPos = productImage.offset();
        const endPos = cartIcon.offset();
        
        flyImage.css({
            position: 'fixed',
            top: startPos.top,
            left: startPos.left,
            width: '50px',
            height: '50px',
            zIndex: 9999,
            borderRadius: '50%'
        });
        
        $('body').append(flyImage);
        
        flyImage.animate({
            top: endPos.top,
            left: endPos.left,
            width: '20px',
            height: '20px',
            opacity: 0.5
        }, 800, function() {
            flyImage.remove();
            cartIcon.addClass('cart-bounce');
            setTimeout(function() {
                cartIcon.removeClass('cart-bounce');
            }, 600);
        });
    }
}

// 搜索功能初始化
function initSearchFunctions() {
    // 搜索建议
    $('.search-input').on('input', function() {
        const query = $(this).val();
        if (query.length > 2) {
            showSearchSuggestions(query);
        } else {
            hideSearchSuggestions();
        }
    });
    
    // 搜索表单提交
    $('.search-form').submit(function(e) {
        e.preventDefault();
        const query = $('.search-input').val();
        if (query.trim()) {
            performSearch(query);
        }
    });
}

// 显示搜索建议
function showSearchSuggestions(query) {
    // 模拟搜索建议数据
    const suggestions = [
        '紫水晶手链',
        '招财黄水晶',
        '辟邪黑曜石',
        '粉水晶项链',
        '白水晶摆件'
    ].filter(item => item.includes(query));
    
    if (suggestions.length > 0) {
        let suggestionHtml = '<div class="search-suggestions">';
        suggestions.forEach(suggestion => {
            suggestionHtml += `<div class="suggestion-item">${suggestion}</div>`;
        });
        suggestionHtml += '</div>';
        
        $('.search-form').append(suggestionHtml);
    }
}

// 隐藏搜索建议
function hideSearchSuggestions() {
    $('.search-suggestions').remove();
}

// 执行搜索
function performSearch(query) {
    showLoading();
    
    setTimeout(function() {
        hideLoading();
        window.location.href = `/search?q=${encodeURIComponent(query)}`;
    }, 500);
}

// 响应式功能初始化
function initResponsiveFunctions() {
    // 移动端菜单切换
    $('.navbar-toggler').click(function() {
        $('.navbar-collapse').toggleClass('show');
    });
    
    // 移动端筛选器切换
    $('.filter-toggle').click(function() {
        $('.sidebar-filters').toggleClass('show');
    });
    
    // 窗口大小改变时的处理
    $(window).resize(function() {
        adjustLayoutForScreenSize();
    });
    
    // 初始调整
    adjustLayoutForScreenSize();
}

// 根据屏幕大小调整布局
function adjustLayoutForScreenSize() {
    const windowWidth = $(window).width();
    
    if (windowWidth < 768) {
        // 移动端调整
        $('.product-grid').removeClass('grid-4 grid-3').addClass('grid-1');
        $('.cart-content').removeClass('grid-2').addClass('grid-1');
    } else if (windowWidth < 992) {
        // 平板端调整
        $('.product-grid').removeClass('grid-4 grid-1').addClass('grid-3');
    } else {
        // 桌面端调整
        $('.product-grid').removeClass('grid-3 grid-1').addClass('grid-4');
        $('.cart-content').removeClass('grid-1').addClass('grid-2');
    }
}

// 工具函数
function showLoading() {
    if (!$('.loading-overlay').length) {
        const loadingHtml = `
            <div class="loading-overlay">
                <div class="loading-spinner">
                    <div class="spinner-ring"></div>
                    <div class="loading-text">加载中...</div>
                </div>
            </div>
        `;
        $('body').append(loadingHtml);
    }
}

function hideLoading() {
    $('.loading-overlay').fadeOut(300, function() {
        $(this).remove();
    });
}

function showNotification(message, type = 'info') {
    const notification = $(`
        <div class="notification notification-${type}">
            <div class="notification-content">
                <i class="notification-icon"></i>
                <span class="notification-message">${message}</span>
            </div>
        </div>
    `);
    
    $('body').append(notification);
    
    setTimeout(function() {
        notification.addClass('show');
    }, 100);
    
    setTimeout(function() {
        notification.removeClass('show');
        setTimeout(function() {
            notification.remove();
        }, 300);
    }, 3000);
}

function updateCartCount() {
    const currentCount = parseInt($('.cart-count').text()) || 0;
    $('.cart-count').text(currentCount + 1);
}

function showEmptyCart() {
    $('.cart-items').html(`
        <div class="empty-cart">
            <div class="empty-icon">🛒</div>
            <div class="empty-title">购物车是空的</div>
            <div class="empty-description">快去选购您喜欢的水晶饰品吧！</div>
            <a href="/categories" class="start-shopping">开始购物</a>
        </div>
    `);
}
