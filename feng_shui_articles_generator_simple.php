<?php

/**
 * 风水玄学水晶饰品销售系统 - 文章数据生成脚本（简化版）
 * 生成风水玄学相关的文章内容
 */

define('IN_BEIKE', true);
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/bootstrap/app.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

// 初始化应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$response = $kernel->handle(
    $request = Illuminate\Http\Request::capture()
);

echo "开始生成风水玄学相关文章数据...\n";

// 获取语言设置
$languages = DB::table('languages')->where('status', 1)->get();
$defaultLanguage = DB::table('languages')->where('code', 'zh_cn')->first();
if (!$defaultLanguage) {
    $defaultLanguage = $languages->first();
}

// 创建文章分类
echo "创建文章分类...\n";

// 检查页面分类表是否存在
$pagesCategoryTableExists = DB::getSchemaBuilder()->hasTable('page_categories');
if (!$pagesCategoryTableExists) {
    echo "错误：页面分类表不存在，无法创建文章分类。\n";
    exit;
}

// 清除现有文章分类
DB::statement('SET FOREIGN_KEY_CHECKS=0');
DB::table('page_category_descriptions')->truncate();
DB::table('page_categories')->truncate();
DB::statement('SET FOREIGN_KEY_CHECKS=1');

// 定义文章分类
$categories = [
    [
        'name' => '风水知识',
        'description' => '传统风水学理论知识，包括阴阳五行、八卦九宫、风水布局等内容。',
        'status' => 1,
        'sort_order' => 0,
    ],
    [
        'name' => '水晶介绍',
        'description' => '各类水晶的特性、功效、历史和使用方法的详细介绍。',
        'status' => 1,
        'sort_order' => 1,
    ],
    [
        'name' => '使用指南',
        'description' => '水晶饰品的使用方法、佩戴禁忌、保养技巧和能量净化方法。',
        'status' => 1,
        'sort_order' => 2,
    ],
    [
        'name' => '客户案例',
        'description' => '真实客户使用水晶饰品后的体验分享和效果反馈。',
        'status' => 1,
        'sort_order' => 3,
    ],
    [
        'name' => '玄学资讯',
        'description' => '玄学相关的最新资讯、时令吉日和传统文化内容。',
        'status' => 1,
        'sort_order' => 4,
    ],
];

// 创建文章分类
$categoryIds = [];
foreach ($categories as $category) {
    $categoryId = DB::table('page_categories')->insertGetId([
        'parent_id' => 0,
        'status' => $category['status'],
        'sort_order' => $category['sort_order'],
        'created_at' => now(),
        'updated_at' => now(),
    ]);
    
    foreach ($languages as $language) {
        DB::table('page_category_descriptions')->insert([
            'page_category_id' => $categoryId,
            'locale' => $language->code,
            'name' => $category['name'],
            'description' => $category['description'],
            'meta_title' => $category['name'] . ' - 灵玉轩',
            'meta_description' => $category['description'],
            'meta_keyword' => str_replace(['，', '、', '。'], ',', $category['name']),
        ]);
    }
    
    $categoryIds[$category['name']] = $categoryId;
    echo "已创建文章分类: {$category['name']}\n";
}

// 文章标题模板
$articleTitles = [
    '风水知识' => [
        '居家风水布局的10个基本原则',
        '办公室风水调整指南：提升事业运势',
        '卧室风水宝典：如何打造高质量睡眠环境',
        '客厅风水布局：招财纳福的核心要素',
        '厨房风水学：影响家庭财运的关键',
        '书房风水布局：提升学习和工作效率',
        '阳台风水调整：化解外煞的有效方法',
        '入户玄关风水：第一道能量屏障的重要性',
        '财位布局详解：如何找到并激活家中财位',
        '五行缺失如何补救：风水平衡的关键',
        '镜子在风水中的运用与禁忌',
        '水在风水中的作用与布置技巧',
        '植物风水学：不同植物的风水作用',
        '颜色在风水中的运用原则',
        '2024年风水方位吉凶详解',
    ],
    
    '水晶介绍' => [
        '紫水晶：提升灵性意识的神秘宝石',
        '白水晶：能量放大器的特性与应用',
        '黄水晶：招财与智慧的双重能量',
        '粉水晶：爱情守护石的能量秘密',
        '黑曜石：强大保护石的使用方法',
        '绿幽灵：商业成功的能量助推器',
        '青金石：古老智慧石的历史与功效',
        '虎眼石：增强自信与决断力的宝石',
        '月光石：连接月亮能量的神秘水晶',
        '茶晶：稳定情绪的大地能量水晶',
        '海蓝宝：沟通与表达的能量催化剂',
        '摩根石：平衡身心的天使之石',
        '碧玺：多彩能量的全能水晶',
        '舒俱来：能量转化器的神奇功效',
        '金发晶：强力招财石的能量特性',
    ],
    
    '使用指南' => [
        '水晶首饰的正确佩戴方法与时间',
        '水晶能量的净化方法：满月净化详解',
        '如何为水晶充能：阳光、月光与意念',
        '水晶摆件的家居布置原则',
        '不同场合应该选择什么样的水晶',
        '水晶饰品的日常保养技巧',
        '水晶手链应该戴在哪只手更有效',
        '水晶搭配的基本原则：能量相容性',
        '水晶的开光与加持：增强能量的方法',
        '初次使用水晶的注意事项',
        '水晶能量耗尽的表现与恢复方法',
        '不同水晶的禁忌与使用限制',
        '水晶手链断裂的风水含义与处理方法',
        '如何判断水晶的品质与真伪',
        '水晶与个人能量场的匹配原理',
    ],
    
    '客户案例' => [
        '【案例分享】事业低谷期遇见绿幽灵的转机',
        '【真实经历】佩戴黑曜石后的奇妙保护力',
        '【用户反馈】粉水晶改善人际关系的实例',
        '【成功案例】白水晶助力考试取得好成绩',
        '【体验分享】紫水晶提升灵感与创造力',
        '【客户故事】黄水晶带来的财运变化',
        '【改善案例】海蓝宝解决沟通障碍的经历',
        '【真实案例】虎眼石增强自信心的效果',
        '【用户分享】水晶摆件改善家居气场的变化',
        '【亲身经历】舒俱来缓解压力的神奇效果',
        '【感谢信】月光石帮助改善睡眠质量',
        '【案例记录】青金石增强判断力的实例',
        '【用户故事】金发晶招财的神奇经历',
        '【成功案例】水晶组合调整五行的效果',
        '【真实分享】碧玺平衡情绪的实际体验',
    ],
    
    '玄学资讯' => [
        '2024年运势分析：12生肖运程详解',
        '夏至节气：阳气至极时的能量调整',
        '农历七月：如何做好能量防护',
        '中秋赏月：增强月亮能量的最佳时机',
        '冬至养生：调整阴阳平衡的重要时刻',
        '清明节气：祭祀与能量清理的传统智慧',
        '2024年最佳转运时机与方法',
        '古法风水中的二十四山向详解',
        '玄空飞星风水2024年九宫飞星图解',
        '紫微斗数与水晶选择的关联',
        '2024年适合结婚的黄道吉日',
        '传统择日学：如何选择开业吉日',
        '奇门遁甲在现代生活中的应用',
        '玄学与现代科学的交汇点探析',
        '道家养生与水晶能量的结合应用',
    ],
];

// 生成文章内容函数（简化版）
function generateArticleContent($title, $category) {
    // 简单的文章内容模板
    $content = "<h1>{$title}</h1>\n\n";
    $content .= "<p>在灵玉轩，我们致力于将古老的风水玄学智慧与现代生活完美结合，帮助您在繁忙的都市生活中找到平衡与和谐。</p>\n\n";
    
    switch ($category) {
        case '风水知识':
            $content .= "<h2>风水的基本原理</h2>\n";
            $content .= "<p>风水学，又称堪舆学，是中国古代一种关于环境与能量场的学问。其核心理念是人与自然的和谐共处，通过调整居住环境来改善人的运势与生活质量。</p>\n\n";
            $content .= "<p>风水讲究「藏风聚气」，意在找到一个既能避开不良气流又能聚集有益能量的理想环境。在现代家居中，我们可以通过合理的布局和装饰来实现这一点。</p>\n\n";
            $content .= "<h2>如何应用风水原理</h2>\n";
            $content .= "<p>在应用风水原理时，我们需要注意以下几点：</p>\n";
            $content .= "<ul>\n";
            $content .= "  <li>了解住宅的坐向和流年方位</li>\n";
            $content .= "  <li>明确各个房间的功能定位</li>\n";
            $content .= "  <li>掌握五行相生相克的原理</li>\n";
            $content .= "  <li>注意能量流动和气场平衡</li>\n";
            $content .= "</ul>\n\n";
            $content .= "<p>通过合理的风水布局，我们可以创造一个有利于健康、财运和事业发展的环境。</p>";
            break;
            
        case '水晶介绍':
            $content .= "<h2>水晶的能量特性</h2>\n";
            $content .= "<p>水晶是地球亿万年演化的产物，每一种水晶都蕴含着独特的能量振动频率。这些能量可以与人体的能量场产生共振，从而影响我们的身心状态。</p>\n\n";
            $content .= "<p>不同的水晶有不同的能量特性，可以针对性地帮助解决各种问题：</p>\n";
            $content .= "<ul>\n";
            $content .= "  <li>紫水晶：增强灵性意识，提升直觉</li>\n";
            $content .= "  <li>白水晶：净化能量，增强其他水晶的功效</li>\n";
            $content .= "  <li>黄水晶：招财、增强自信心</li>\n";
            $content .= "  <li>黑曜石：保护能量场，抵御负面能量</li>\n";
            $content .= "</ul>\n\n";
            $content .= "<h2>如何选择合适的水晶</h2>\n";
            $content .= "<p>选择水晶时，可以根据自己的需求和直觉来决定。有时候，我们会被某种水晶特别吸引，这往往意味着这种水晶的能量正是我们所需要的。</p>";
            break;
            
        case '使用指南':
            $content .= "<h2>水晶的使用方法</h2>\n";
            $content .= "<p>水晶的使用方法多种多样，包括佩戴、摆放、冥想等。不同的使用方式会产生不同的效果。</p>\n\n";
            $content .= "<h3>佩戴方法</h3>\n";
            $content .= "<p>佩戴水晶是最常见也是最直接的使用方式。水晶手链通常佩戴在左手以接收能量，右手以释放能量。但这也要根据个人情况和需求来调整。</p>\n\n";
            $content .= "<h3>摆放方法</h3>\n";
            $content .= "<p>水晶摆件可以放置在家中或办公室的特定位置，如财位、事业位等，以增强相应的能量场。</p>\n\n";
            $content .= "<h2>水晶的保养</h2>\n";
            $content .= "<p>水晶需要定期净化和充能，以保持其能量的纯净和活力。净化方法包括：</p>\n";
            $content .= "<ul>\n";
            $content .= "  <li>月光净化：满月之夜放在月光下一整晚</li>\n";
            $content .= "  <li>流水净化：用清水冲洗15-20分钟</li>\n";
            $content .= "  <li>香薰净化：用藏香或白鼠尾草熏烟净化</li>\n";
            $content .= "</ul>";
            break;
            
        case '客户案例':
            $content .= "<h2>客户背景</h2>\n";
            $content .= "<p>王先生，35岁，企业中层管理人员，近期面临工作压力大、事业发展遇阻的困境。</p>\n\n";
            $content .= "<h2>咨询过程</h2>\n";
            $content .= "<p>王先生来到灵玉轩咨询后，我们根据他的生辰八字和具体情况，为他推荐了一款定制的水晶手链，主要成分为绿幽灵、黑曜石和虎眼石的组合。</p>\n\n";
            $content .= "<h2>使用效果</h2>\n";
            $content .= "<p>佩戴水晶手链一个月后，王先生反馈工作中的决策更加果断，思路更加清晰，并且成功争取到了一个重要项目的负责权。</p>\n\n";
            $content .= "<h2>客户感言</h2>\n";
            $content .= "<p>「起初我对水晶的功效持怀疑态度，但抱着试一试的心态购买了灵玉轩的水晶手链。令我惊讶的是，佩戴后不仅心情变得平静，工作效率也有了明显提升。现在这款手链已经成为我日常不可或缺的物品了。」</p>";
            break;
            
        case '玄学资讯':
            $content .= "<h2>传统智慧与现代生活</h2>\n";
            $content .= "<p>中国传统玄学包含了丰富的智慧，这些智慧在现代生活中依然具有重要的指导意义。</p>\n\n";
            $content .= "<h2>本月运势分析</h2>\n";
            $content .= "<p>根据传统历法和星象变化，本月整体运势偏向稳定，适合稳扎稳打、循序渐进地推进各项计划。</p>\n\n";
            $content .= "<h3>财运</h3>\n";
            $content .= "<p>本月财运平稳，适合理财规划和长期投资，不宜进行高风险投机。</p>\n\n";
            $content .= "<h3>事业</h3>\n";
            $content .= "<p>工作上可能面临一些挑战，但只要保持耐心和专注，定能迎刃而解。建议在办公环境中放置一些能量水晶，如黄水晶或绿幽灵，以增强事业运势。</p>\n\n";
            $content .= "<h2>开运建议</h2>\n";
            $content .= "<p>1. 保持居家环境整洁，特别是财位和事业位</p>\n";
            $content .= "<p>2. 佩戴或摆放适合本月能量的水晶</p>\n";
            $content .= "<p>3. 注意作息规律，保持身心平衡</p>";
            break;
            
        default:
            $content .= "<p>更多精彩内容，敬请期待...</p>";
    }
    
    return $content;
}

// 清除现有文章
echo "清除现有文章数据...\n";
DB::statement('SET FOREIGN_KEY_CHECKS=0');
DB::table('page_descriptions')->truncate();
DB::table('pages')->truncate();
DB::statement('SET FOREIGN_KEY_CHECKS=1');

// 生成文章数据
echo "开始生成文章数据...\n";
$articleCount = 0;
$targetCount = 200; // 目标生成200篇文章

foreach ($articleTitles as $category => $titles) {
    $categoryId = $categoryIds[$category] ?? 0;
    
    foreach ($titles as $title) {
        try {
            // 插入文章基本信息
            $pageId = DB::table('pages')->insertGetId([
                'page_category_id' => $categoryId,
                'author' => '灵玉轩专家团队',
                'image' => 'https://img.alicdn.com/imgextra/i3/2201504856948/O1CN01z5CtXW1zGnyiHXEt9_!!2201504856948.jpg',
                'video' => '',
                'views' => mt_rand(100, 5000),
                'active' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            
            // 插入文章内容（多语言支持）
            foreach ($languages as $language) {
                DB::table('page_descriptions')->insert([
                    'page_id' => $pageId,
                    'locale' => $language->code,
                    'title' => $title,
                    'content' => generateArticleContent($title, $category),
                    'summary' => substr(strip_tags(generateArticleContent($title, $category)), 0, 200) . '...',
                    'meta_title' => $title . ' - 灵玉轩',
                    'meta_description' => substr(strip_tags(generateArticleContent($title, $category)), 0, 150) . '...',
                    'meta_keyword' => str_replace(['，', '、', '。'], ',', $title . ',' . $category . ',灵玉轩,风水,玄学,水晶'),
                ]);
            }
            
            $articleCount++;
            
            if ($articleCount % 10 == 0) {
                echo "已生成 {$articleCount} 篇文章...\n";
            }
            
            // 复制一些文章以达到目标数量
            if ($articleCount >= count($articleTitles) * count($titles) && $articleCount < $targetCount) {
                $copyCount = min(5, $targetCount - $articleCount);
                for ($i = 0; $i < $copyCount; $i++) {
                    $copyTitle = $title . ' (' . ($i + 2) . ')';
                    
                    $copyPageId = DB::table('pages')->insertGetId([
                        'page_category_id' => $categoryId,
                        'author' => '灵玉轩专家团队',
                        'image' => 'https://img.alicdn.com/imgextra/i3/2201504856948/O1CN01z5CtXW1zGnyiHXEt9_!!2201504856948.jpg',
                        'video' => '',
                        'views' => mt_rand(100, 5000),
                        'active' => 1,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                    
                    foreach ($languages as $language) {
                        DB::table('page_descriptions')->insert([
                            'page_id' => $copyPageId,
                            'locale' => $language->code,
                            'title' => $copyTitle,
                            'content' => generateArticleContent($copyTitle, $category),
                            'summary' => substr(strip_tags(generateArticleContent($copyTitle, $category)), 0, 200) . '...',
                            'meta_title' => $copyTitle . ' - 灵玉轩',
                            'meta_description' => substr(strip_tags(generateArticleContent($copyTitle, $category)), 0, 150) . '...',
                            'meta_keyword' => str_replace(['，', '、', '。'], ',', $copyTitle . ',' . $category . ',灵玉轩,风水,玄学,水晶'),
                        ]);
                    }
                    
                    $articleCount++;
                    
                    if ($articleCount % 10 == 0) {
                        echo "已生成 {$articleCount} 篇文章...\n";
                    }
                    
                    if ($articleCount >= $targetCount) {
                        break;
                    }
                }
            }
            
            if ($articleCount >= $targetCount) {
                break;
            }
        } catch (Exception $e) {
            echo "生成文章时出错: " . $e->getMessage() . "\n";
        }
    }
    
    if ($articleCount >= $targetCount) {
        break;
    }
}

echo "风水玄学相关文章数据生成完成！\n";
echo "共成功生成 {$articleCount} 篇文章。\n";
echo "请前往管理后台 -> 内容 -> 文章 查看生成的文章。\n";
