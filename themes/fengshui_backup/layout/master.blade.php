<!doctype html>
<html lang="{{ locale() }}">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <meta name="csrf-token" content="{{ csrf_token() }}">
  <title>@yield('title', system_setting('base.meta_title', '风水玄学水晶饰品 - 招财辟邪增运健康'))</title>
  <meta name="keywords" content="@yield('keywords', system_setting('base.meta_keywords', '风水,玄学,水晶,饰品,招财,辟邪,增运,健康,紫水晶,黄水晶,黑曜石'))">
  <meta name="description" content="@yield('description', system_setting('base.meta_description', '专业风水玄学水晶饰品商城，提供招财、辟邪、增运、健康等功效水晶，包括紫水晶、黄水晶、黑曜石等天然水晶饰品'))">
  <meta name="generator" content="BeikeShop v{{ config('beike.version') }}({{ config('beike.build') }})">
  <base href="{{ $shop_base_url }}">

  <!-- 风水主题样式 -->
  <link rel="stylesheet" type="text/css" href="{{ mix('/build/beike/shop/'.system_setting('base.theme').'/css/bootstrap.css') }}">
  <link rel="stylesheet" type="text/css" href="{{ mix('/build/beike/shop/'.system_setting('base.theme').'/css/app.css') }}">

  <!-- 字体和图标 -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

  <!-- JavaScript库 -->
  <script src="{{ asset('vendor/jquery/jquery-3.6.0.min.js') }}"></script>
  <script src="{{ asset('vendor/layer/3.5.1/layer.js') }}"></script>
  <script src="{{ asset('vendor/lazysizes/lazysizes.min.js') }}"></script>
  <script src="{{ asset('vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
  <script src="{{ mix('/build/beike/shop/'.system_setting('base.theme').'/js/app.js') }}"></script>

  <link rel="shortcut icon" href="{{ image_origin(system_setting('base.favicon')) }}">

  @if (system_setting('base.head_code'))
    {!! system_setting('base.head_code') !!}
  @endif
  @hook('layout.header.code')
  @stack('header')

  <!-- 风水主题特有样式 -->
  <style>
    :root {
      --fengshui-primary: #2d1b69;
      --fengshui-secondary: #1a237e;
      --fengshui-accent: #ffd700;
      --fengshui-dark: #0d0d0d;
      --fengshui-text-light: #ffffff;
      --fengshui-text-gold: #ffd700;
      --fengshui-text-muted: #b0b0b0;
      --fengshui-bg-dark: #0f0f0f;
      --fengshui-bg-card: #1a1a1a;
      --fengshui-border: #333333;
    }

    body {
      font-family: 'Noto Sans SC', 'Microsoft YaHei', sans-serif;
      background: var(--fengshui-bg-dark);
      color: var(--fengshui-text-light);
    }

    /* 加载动画样式 */
    .loading-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(15, 15, 15, 0.9);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
    }

    .loading-spinner {
      text-align: center;
      color: var(--fengshui-accent);
    }

    .spinner-ring {
      width: 50px;
      height: 50px;
      border: 3px solid rgba(255, 215, 0, 0.3);
      border-top: 3px solid var(--fengshui-accent);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 15px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* 通知样式 */
    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      background: var(--fengshui-bg-card);
      border: 1px solid var(--fengshui-border);
      border-radius: 8px;
      padding: 15px 20px;
      color: var(--fengshui-text-light);
      z-index: 9999;
      transform: translateX(100%);
      transition: transform 0.3s ease;
      box-shadow: 0 4px 20px rgba(45, 27, 105, 0.3);
    }

    .notification.show {
      transform: translateX(0);
    }

    .notification-success {
      border-left: 4px solid #27ae60;
    }

    .notification-error {
      border-left: 4px solid #e74c3c;
    }

    .notification-info {
      border-left: 4px solid var(--fengshui-accent);
    }
  </style>
</head>
<body class="@yield('body-class') {{ request('_from') }}">
  @if (!request('iframe') && request('_from') != 'app')
    @include('layout.header')
  @endif

  @yield('content')

  @if (!request('iframe') && request('_from') != 'app')
    @include('layout.footer')
  @endif

  <script>
    const config = {
      isLogin: !!{{ current_customer()->id ?? 'null' }},
      guestCheckout: !!{{ system_setting('base.guest_checkout', 1) }},
      loginShowPrice: !!{{ system_setting('base.show_price_after_login', 0) }},
    }

    // 如果页面使用了ElementUI，且当前语言不是中文，则加载对应的语言包
    @if (locale() != 'zh_cn')
    if (typeof ELEMENT !== 'undefined') {
        const elLocale = '{{ asset('vendor/element-ui/language/'.locale().'.js') }}';
        document.write("<script src='" + elLocale + "'><\/script>")

        $(function () {
          setTimeout(() => {
            ELEMENT.locale(ELEMENT.lang['{{ locale() }}'])
          }, 0);
        })
      }
    @endif
  </script>

  @if (strpos($_SERVER['SERVER_SOFTWARE'], 'nginx') !== false)
    <div class="nginx-alert d-none">{!! __('shop/common.nginx_alert') !!}</div>
  @endif

  @stack('add-scripts')
</body>
<!-- BeikeShop v{{ config('beike.version') }}({{ config('beike.build') }}) -->
</html>
