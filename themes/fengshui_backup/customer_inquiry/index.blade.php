@extends('layout.master')

@section('title', $page_title)
@section('description', $page_description)

@section('content')
<div class="container">
  <div class="row justify-content-center">
    <div class="col-lg-8">
      <div class="card">
        <div class="card-header">
          <h4 class="mb-0">{{ trans('shop/customer_inquiry.page_title') }}</h4>
        </div>
        <div class="card-body">
          <div class="alert alert-info">
            <i class="bi bi-info-circle me-2"></i>
            {{ trans('shop/customer_inquiry.page_description') }}
          </div>

          <form id="customer-inquiry-form" class="needs-validation" novalidate>
            @csrf
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="name" class="form-label">{{ trans('shop/customer_inquiry.name') }} <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="name" name="name" maxlength="50" required>
                <div class="invalid-feedback">
                  {{ trans('shop/customer_inquiry.name_required') }}
                </div>
              </div>
              <div class="col-md-6 mb-3">
                <label for="phone" class="form-label">{{ trans('shop/customer_inquiry.phone') }} <span class="text-danger">*</span></label>
                <input type="tel" class="form-control" id="phone" name="phone" maxlength="20" required>
                <div class="invalid-feedback">
                  {{ trans('shop/customer_inquiry.phone_required') }}
                </div>
              </div>
            </div>

            <div class="mb-3">
              <label for="email" class="form-label">{{ trans('shop/customer_inquiry.email') }} <span class="text-danger">*</span></label>
              <input type="email" class="form-control" id="email" name="email" required>
              <div class="invalid-feedback">
                {{ trans('shop/customer_inquiry.email_required') }}
              </div>
            </div>

            {{-- 预设问题 --}}
            <div class="row">
              <div class="col-md-6 mb-3">
                <label class="form-label">{{ trans('shop/customer_inquiry.favorite_color') }} <span class="text-danger">*</span></label>
                <div class="mt-2">
                  @foreach($favorite_color_options as $value => $label)
                    <div class="form-check form-check-inline">
                      <input class="form-check-input" type="radio" name="favorite_color" id="color_{{ $value }}" value="{{ $value }}" required>
                      <label class="form-check-label" for="color_{{ $value }}">
                        {{ $label }}
                      </label>
                    </div>
                  @endforeach
                </div>
                <div class="invalid-feedback">
                  {{ trans('shop/customer_inquiry.favorite_color_required') }}
                </div>
              </div>

              <div class="col-md-6 mb-3">
                <label class="form-label">{{ trans('shop/customer_inquiry.afraid_of_cold') }} <span class="text-danger">*</span></label>
                <div class="mt-2">
                  @foreach($afraid_of_cold_options as $value => $label)
                    <div class="form-check form-check-inline">
                      <input class="form-check-input" type="radio" name="afraid_of_cold" id="cold_{{ $value }}" value="{{ $value }}" required>
                      <label class="form-check-label" for="cold_{{ $value }}">
                        {{ $label }}
                      </label>
                    </div>
                  @endforeach
                </div>
                <div class="invalid-feedback">
                  {{ trans('shop/customer_inquiry.afraid_of_cold_required') }}
                </div>
              </div>
            </div>

            <div class="mb-3">
              <label class="form-label">{{ trans('shop/customer_inquiry.happiest_thing') }} <span class="text-danger">*</span></label>
              <div class="mt-2">
                @foreach($happiest_thing_options as $value => $label)
                  <div class="form-check">
                    <input class="form-check-input" type="radio" name="happiest_thing" id="happy_{{ $value }}" value="{{ $value }}" required>
                    <label class="form-check-label" for="happy_{{ $value }}">
                      {{ $label }}
                    </label>
                  </div>
                @endforeach
              </div>
              <div class="invalid-feedback">
                {{ trans('shop/customer_inquiry.happiest_thing_required') }}
              </div>
            </div>

            <div class="mb-3">
              <label for="content" class="form-label">{{ trans('shop/customer_inquiry.content') }} <span class="text-danger">*</span></label>
              <textarea class="form-control" id="content" name="content" rows="6" maxlength="500" required placeholder="{{ trans('shop/customer_inquiry.content_placeholder') }}"></textarea>
              <div class="form-text">
                <span id="content-count">0</span>/500 {{ trans('shop/customer_inquiry.characters') }}
              </div>
              <div class="invalid-feedback">
                {{ trans('shop/customer_inquiry.content_required') }}
              </div>
            </div>

            <div class="d-grid gap-2">
              <button type="submit" class="btn btn-primary btn-lg" id="submit-btn">
                <i class="bi bi-send me-2"></i>
                {{ trans('shop/customer_inquiry.submit') }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

@push('add-scripts')
<script>
$(document).ready(function() {
  // 字符计数
  $('#content').on('input', function() {
    const length = $(this).val().length;
    $('#content-count').text(length);

    if (length > 500) {
      $(this).addClass('is-invalid');
    } else {
      $(this).removeClass('is-invalid');
    }
  });

  // 手机号验证
  $('#phone').on('blur', function() {
    const phone = $(this).val();
    const phoneRegex = /^1[3-9]\d{9}$/;

    if (phone && !phoneRegex.test(phone)) {
      $(this).addClass('is-invalid');
      $(this).siblings('.invalid-feedback').text('{{ trans("shop/customer_inquiry.phone_format_error") }}');
    } else {
      $(this).removeClass('is-invalid');
    }
  });

  // 表单提交
  $('#customer-inquiry-form').on('submit', function(e) {
    e.preventDefault();

    const form = this;
    if (!form.checkValidity()) {
      e.stopPropagation();
      form.classList.add('was-validated');
      return;
    }

    const submitBtn = $('#submit-btn');
    const originalText = submitBtn.html();

    // 禁用提交按钮
    submitBtn.prop('disabled', true).html('<i class="spinner-border spinner-border-sm me-2"></i>{{ trans("shop/customer_inquiry.submitting") }}');

    $.ajax({
      url: '{{ shop_route("customer_inquiry.store") }}',
      method: 'POST',
      data: $(this).serialize(),
      headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
        'X-Requested-With': 'XMLHttpRequest'
      },
      success: function(response) {
        // 确保响应是对象
        if (typeof response === 'string') {
          try {
            response = JSON.parse(response);
          } catch (e) {
            alert('服务器响应格式错误，请重试');
            return;
          }
        }

        if (response && response.status === 'success') {
          // 使用多种方式确保提示显示
          if (typeof layer !== 'undefined') {
            layer.msg(response.message || '提交成功！', {icon: 1, time: 2000}, function() {
              window.location.href = '{{ shop_route("customer_inquiry.success") }}';
            });
          } else {
            alert(response.message || '提交成功！感谢您的咨询，我们会尽快回复您。');
            setTimeout(function() {
              window.location.href = '{{ shop_route("customer_inquiry.success") }}';
            }, 1500);
          }
        } else {
          if (typeof layer !== 'undefined') {
            layer.msg(response.message || '提交失败，请重试', {icon: 2});
          } else {
            alert(response.message || '提交失败，请重试');
          }
        }
      },
      error: function(xhr) {
        let message = '提交失败，请稍后重试';

        if (xhr.responseJSON) {
          if (xhr.responseJSON.message) {
            message = xhr.responseJSON.message;
          } else if (xhr.responseJSON.errors) {
            const errors = Object.values(xhr.responseJSON.errors).flat();
            message = '表单验证失败：\n' + errors.join('\n');
          }
        }

        if (typeof layer !== 'undefined') {
          layer.msg(message, {icon: 2});
        } else {
          alert(message);
        }
      },
      complete: function() {
        // 恢复提交按钮
        submitBtn.prop('disabled', false).html(originalText);
      }
    });
  });
});
</script>
@endpush
@endsection
