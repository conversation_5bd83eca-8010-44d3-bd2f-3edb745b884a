@extends('layout.master')

@section('title', '玄晶阁 - 专业风水水晶饰品，助您改善运势')
@section('description', '玄晶阁专注于高品质风水水晶饰品，提供招财、辟邪、增运、健康等功效水晶，传承千年玄学智慧，为您的生活带来正能量与好运。')
@section('keywords', '风水水晶,水晶饰品,招财水晶,辟邪水晶,增运水晶,健康水晶,紫水晶,白水晶,黄水晶,粉水晶,黑曜石,水晶手链,水晶项链,水晶摆件')

@push('styles')
<style>
.hero-section {
  background: linear-gradient(135deg, rgba(26, 13, 46, 0.9), rgba(45, 27, 78, 0.9)), 
              url('{{ asset("themes/crystal/images/hero-bg.jpg") }}') center/cover;
  min-height: 80vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 70%, rgba(212, 175, 55, 0.1) 0%, transparent 50%);
  animation: mysticalPulse 4s ease-in-out infinite alternate;
}

@keyframes mysticalPulse {
  0% { opacity: 0.3; }
  100% { opacity: 0.7; }
}

.hero-content {
  position: relative;
  z-index: 2;
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: var(--gradient-gold);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  color: var(--primary-dark);
  font-size: 2rem;
  transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
  transform: scale(1.1) rotate(10deg);
  box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
}

.category-card {
  background: var(--bg-card);
  border: var(--border-mystical);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.4s ease;
  position: relative;
  height: 300px;
}

.category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-mystical);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.category-card:hover::before {
  opacity: 0.2;
}

.category-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(212, 175, 55, 0.3);
}

.category-image {
  height: 200px;
  background-size: cover;
  background-position: center;
  position: relative;
}

.category-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.7) 100%);
  display: flex;
  align-items: end;
  padding: 20px;
}

.category-title {
  color: var(--text-primary);
  font-family: var(--font-mystical);
  font-size: 1.5rem;
  font-weight: 600;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.category-info {
  padding: 20px;
  position: relative;
  z-index: 2;
}

.testimonial-card {
  background: var(--bg-card);
  border: var(--border-mystical);
  border-radius: 16px;
  padding: 30px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.testimonial-card::before {
  content: '"';
  position: absolute;
  top: -10px;
  left: 20px;
  font-size: 6rem;
  color: var(--accent-gold);
  opacity: 0.3;
  font-family: serif;
}

.testimonial-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 3px solid var(--accent-gold);
  margin: 0 auto 1rem;
  overflow: hidden;
}

.stats-section {
  background: var(--gradient-mystical);
  padding: 60px 0;
  position: relative;
}

.stats-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="stars" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23d4af37" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23stars)"/></svg>');
  opacity: 0.5;
}

.stat-item {
  text-align: center;
  position: relative;
  z-index: 2;
}

.stat-number {
  font-size: 3rem;
  font-weight: 700;
  color: var(--accent-gold);
  font-family: var(--font-mystical);
}

.stat-label {
  color: var(--text-secondary);
  font-size: 1.1rem;
  margin-top: 0.5rem;
}
</style>
@endpush

@section('content')
<!-- 英雄区域 -->
<section class="hero-section">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-lg-6">
        <div class="hero-content">
          <h1 class="display-4 fw-bold mb-4 mystical-glow">
            玄晶阁
            <br>
            <span class="text-warning">千年玄学智慧</span>
          </h1>
          <p class="lead mb-4 text-light">
            传承千年的风水玄学智慧，精选天然水晶饰品，为您的生活带来正能量与好运。每一件水晶都经过精心挑选，蕴含着大自然的神秘力量。
          </p>
          <div class="d-flex gap-3 flex-wrap">
            <a href="{{ shop_route('products.index') }}" class="btn btn-mystical btn-lg">
              <i class="bi bi-shop me-2"></i>立即选购
            </a>
            <a href="{{ shop_route('pages.show', 'guide') }}" class="btn btn-secondary-mystical btn-lg">
              <i class="bi bi-book me-2"></i>选购指南
            </a>
          </div>
        </div>
      </div>
      <div class="col-lg-6 text-center">
        <div class="crystal-sparkle">
          <img src="{{ asset('themes/crystal/images/hero-crystal.png') }}" alt="水晶饰品" class="img-fluid" style="max-height: 500px;">
        </div>
      </div>
    </div>
  </div>
</section>

<!-- 特色功能 -->
<section class="py-5">
  <div class="container">
    <div class="row text-center mb-5">
      <div class="col-12">
        <h2 class="display-5 fw-bold mb-3 mystical-glow">为什么选择玄晶阁</h2>
        <p class="lead text-muted">专业品质，用心服务，让每一位客户都能感受到水晶的神奇力量</p>
      </div>
    </div>
    
    <div class="row g-4">
      <div class="col-lg-3 col-md-6">
        <div class="feature-card text-center h-100">
          <div class="feature-icon">
            <i class="bi bi-gem"></i>
          </div>
          <h5 class="fw-bold mb-3">天然正品</h5>
          <p class="text-muted">所有水晶均为天然开采，经过专业鉴定，确保每一件都是正品天然水晶。</p>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="feature-card text-center h-100">
          <div class="feature-icon">
            <i class="bi bi-yin-yang"></i>
          </div>
          <h5 class="fw-bold mb-3">风水加持</h5>
          <p class="text-muted">每件水晶都经过风水大师开光加持，增强其灵性和功效。</p>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="feature-card text-center h-100">
          <div class="feature-icon">
            <i class="bi bi-shield-check"></i>
          </div>
          <h5 class="fw-bold mb-3">品质保证</h5>
          <p class="text-muted">提供30天无理由退换，终身保养服务，让您购买无忧。</p>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="feature-card text-center h-100">
          <div class="feature-icon">
            <i class="bi bi-person-hearts"></i>
          </div>
          <h5 class="fw-bold mb-3">专业指导</h5>
          <p class="text-muted">专业风水师一对一指导，帮您选择最适合的水晶饰品。</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- 热门分类 -->
<section class="py-5 bg-dark">
  <div class="container">
    <div class="row text-center mb-5">
      <div class="col-12">
        <h2 class="display-5 fw-bold mb-3 mystical-glow">热门分类</h2>
        <p class="lead text-muted">根据不同需求，为您精心分类的水晶饰品</p>
      </div>
    </div>
    
    <div class="row g-4">
      <div class="col-lg-3 col-md-6">
        <div class="category-card">
          <div class="category-image" style="background-image: url('{{ asset('themes/crystal/images/category-wealth.jpg') }}');">
            <div class="category-overlay">
              <h4 class="category-title">招财水晶</h4>
            </div>
          </div>
          <div class="category-info">
            <p class="text-muted mb-3">黄水晶、绿幽灵等招财水晶，助您财运亨通</p>
            <a href="#" class="btn btn-secondary-mystical btn-sm">查看更多</a>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="category-card">
          <div class="category-image" style="background-image: url('{{ asset('themes/crystal/images/category-protection.jpg') }}');">
            <div class="category-overlay">
              <h4 class="category-title">辟邪水晶</h4>
            </div>
          </div>
          <div class="category-info">
            <p class="text-muted mb-3">黑曜石、黑碧玺等辟邪水晶，护佑平安</p>
            <a href="#" class="btn btn-secondary-mystical btn-sm">查看更多</a>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="category-card">
          <div class="category-image" style="background-image: url('{{ asset('themes/crystal/images/category-love.jpg') }}');">
            <div class="category-overlay">
              <h4 class="category-title">爱情水晶</h4>
            </div>
          </div>
          <div class="category-info">
            <p class="text-muted mb-3">粉水晶、红纹石等爱情水晶，增进感情</p>
            <a href="#" class="btn btn-secondary-mystical btn-sm">查看更多</a>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="category-card">
          <div class="category-image" style="background-image: url('{{ asset('themes/crystal/images/category-health.jpg') }}');">
            <div class="category-overlay">
              <h4 class="category-title">健康水晶</h4>
            </div>
          </div>
          <div class="category-info">
            <p class="text-muted mb-3">白水晶、紫水晶等健康水晶，守护健康</p>
            <a href="#" class="btn btn-secondary-mystical btn-sm">查看更多</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- 精选商品 -->
<section class="py-5">
  <div class="container">
    <div class="row text-center mb-5">
      <div class="col-12">
        <h2 class="display-5 fw-bold mb-3 mystical-glow">精选商品</h2>
        <p class="lead text-muted">精心挑选的热门水晶饰品，深受客户喜爱</p>
      </div>
    </div>
    
    <div class="row g-4">
      @foreach($featured_products ?? [] as $product)
        <div class="col-lg-3 col-md-6">
          <div class="product-card">
            @if($product->images->isNotEmpty())
              <div class="product-image">
                <img src="{{ $product->images->first()->path }}" alt="{{ $product->description->name }}" class="w-100">
                @if($product->is_featured)
                  <div class="product-badge">精选</div>
                @endif
              </div>
            @endif
            
            <div class="product-info">
              <h5 class="product-title">{{ $product->description->name }}</h5>
              <p class="product-description">{{ Str::limit($product->description->summary, 60) }}</p>
              <div class="product-price">
                ¥{{ number_format($product->price, 2) }}
                @if($product->origin_price > $product->price)
                  <span class="original-price">¥{{ number_format($product->origin_price, 2) }}</span>
                @endif
              </div>
              <button class="btn btn-mystical w-100 add-to-cart" data-product-id="{{ $product->id }}">
                <i class="bi bi-cart-plus me-2"></i>加入购物车
              </button>
            </div>
          </div>
        </div>
      @endforeach
    </div>
    
    <div class="text-center mt-5">
      <a href="{{ shop_route('products.index') }}" class="btn btn-secondary-mystical btn-lg">
        <i class="bi bi-grid me-2"></i>查看全部商品
      </a>
    </div>
  </div>
</section>

<!-- 数据统计 -->
<section class="stats-section">
  <div class="container">
    <div class="row g-4">
      <div class="col-lg-3 col-md-6">
        <div class="stat-item">
          <div class="stat-number" data-count="50000">0</div>
          <div class="stat-label">满意客户</div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="stat-item">
          <div class="stat-number" data-count="1000">0</div>
          <div class="stat-label">精品水晶</div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="stat-item">
          <div class="stat-number" data-count="15">0</div>
          <div class="stat-label">服务年限</div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="stat-item">
          <div class="stat-number" data-count="99">0</div>
          <div class="stat-label">好评率%</div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- 客户评价 -->
<section class="py-5">
  <div class="container">
    <div class="row text-center mb-5">
      <div class="col-12">
        <h2 class="display-5 fw-bold mb-3 mystical-glow">客户评价</h2>
        <p class="lead text-muted">听听客户们的真实反馈</p>
      </div>
    </div>
    
    <div class="row g-4">
      <div class="col-lg-4">
        <div class="testimonial-card">
          <div class="testimonial-avatar">
            <img src="{{ asset('themes/crystal/images/avatar1.jpg') }}" alt="客户头像" class="w-100 h-100 object-fit-cover">
          </div>
          <h6 class="fw-bold mb-2">张女士</h6>
          <p class="text-muted mb-3">购买了招财黄水晶手链后，工作上确实有了很多机会，非常感谢玄晶阁的专业服务。</p>
          <div class="text-warning">
            <i class="bi bi-star-fill"></i>
            <i class="bi bi-star-fill"></i>
            <i class="bi bi-star-fill"></i>
            <i class="bi bi-star-fill"></i>
            <i class="bi bi-star-fill"></i>
          </div>
        </div>
      </div>
      
      <div class="col-lg-4">
        <div class="testimonial-card">
          <div class="testimonial-avatar">
            <img src="{{ asset('themes/crystal/images/avatar2.jpg') }}" alt="客户头像" class="w-100 h-100 object-fit-cover">
          </div>
          <h6 class="fw-bold mb-2">李先生</h6>
          <p class="text-muted mb-3">黑曜石辟邪摆件放在办公室后，工作环境明显改善，同事关系也更和谐了。</p>
          <div class="text-warning">
            <i class="bi bi-star-fill"></i>
            <i class="bi bi-star-fill"></i>
            <i class="bi bi-star-fill"></i>
            <i class="bi bi-star-fill"></i>
            <i class="bi bi-star-fill"></i>
          </div>
        </div>
      </div>
      
      <div class="col-lg-4">
        <div class="testimonial-card">
          <div class="testimonial-avatar">
            <img src="{{ asset('themes/crystal/images/avatar3.jpg') }}" alt="客户头像" class="w-100 h-100 object-fit-cover">
          </div>
          <h6 class="fw-bold mb-2">王女士</h6>
          <p class="text-muted mb-3">粉水晶项链戴了一个月，感情生活确实有了积极的变化，很神奇的体验。</p>
          <div class="text-warning">
            <i class="bi bi-star-fill"></i>
            <i class="bi bi-star-fill"></i>
            <i class="bi bi-star-fill"></i>
            <i class="bi bi-star-fill"></i>
            <i class="bi bi-star-fill"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
  // 数字动画
  function animateNumbers() {
    $('.stat-number').each(function() {
      const $this = $(this);
      const countTo = $this.data('count');
      
      $({ countNum: 0 }).animate({
        countNum: countTo
      }, {
        duration: 2000,
        easing: 'swing',
        step: function() {
          $this.text(Math.floor(this.countNum).toLocaleString());
        },
        complete: function() {
          $this.text(countTo.toLocaleString());
        }
      });
    });
  }
  
  // 当统计区域进入视窗时触发动画
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        animateNumbers();
        observer.unobserve(entry.target);
      }
    });
  });
  
  observer.observe(document.querySelector('.stats-section'));
});
</script>
@endpush
