<!DOCTYPE html>
<html lang="{{ locale() }}">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="csrf-token" content="{{ csrf_token() }}">
  
  <title>@yield('title', '玄晶阁 - 专业风水水晶饰品')</title>
  <meta name="description" content="@yield('description', '玄晶阁专注于高品质风水水晶饰品，提供招财、辟邪、增运等功效水晶，助您改善运势，提升生活品质。')">
  <meta name="keywords" content="@yield('keywords', '风水水晶,水晶饰品,招财水晶,辟邪水晶,紫水晶,白水晶,黄水晶,粉水晶,黑曜石')">
  
  <!-- Open Graph -->
  <meta property="og:title" content="@yield('title', '玄晶阁 - 专业风水水晶饰品')">
  <meta property="og:description" content="@yield('description', '玄晶阁专注于高品质风水水晶饰品')">
  <meta property="og:image" content="@yield('og_image', asset('themes/crystal/images/logo-og.jpg'))">
  <meta property="og:url" content="{{ url()->current() }}">
  <meta property="og:type" content="website">
  
  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="{{ asset('themes/crystal/images/favicon.ico') }}">
  <link rel="apple-touch-icon" href="{{ asset('themes/crystal/images/apple-touch-icon.png') }}">
  
  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  
  <!-- Bootstrap Icons -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
  
  <!-- Custom Theme CSS -->
  <link href="{{ asset('themes/crystal/css/mystical-theme.css') }}" rel="stylesheet">
  
  <!-- Additional CSS -->
  @stack('styles')
  
  <!-- Schema.org structured data -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "Store",
    "name": "玄晶阁",
    "description": "专业风水水晶饰品销售",
    "url": "{{ url('/') }}",
    "logo": "{{ asset('themes/crystal/images/logo.png') }}",
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "CN"
    },
    "priceRange": "$$"
  }
  </script>
</head>

<body class="mystical-theme">
  <!-- 页面加载动画 -->
  <div id="page-loader" class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background: var(--bg-primary); z-index: 9999;">
    <div class="text-center">
      <div class="loading-mystical mb-3"></div>
      <p class="text-muted">正在加载玄晶阁...</p>
    </div>
  </div>

  <!-- 导航栏 -->
  <nav class="navbar navbar-expand-lg navbar-mystical fixed-top">
    <div class="container">
      <a class="navbar-brand mystical-glow" href="{{ shop_route('home.index') }}">
        <i class="bi bi-gem me-2"></i>玄晶阁
      </a>
      
      <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav me-auto">
          <li class="nav-item">
            <a class="nav-link" href="{{ shop_route('home.index') }}">
              <i class="bi bi-house me-1"></i>首页
            </a>
          </li>
          <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
              <i class="bi bi-grid me-1"></i>水晶分类
            </a>
            <ul class="dropdown-menu">
              @foreach(categories() as $category)
                <li><a class="dropdown-item" href="{{ shop_route('categories.show', $category) }}">{{ $category->description->name }}</a></li>
              @endforeach
            </ul>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ shop_route('products.index') }}">
              <i class="bi bi-shop me-1"></i>全部商品
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ shop_route('pages.index') }}">
              <i class="bi bi-book me-1"></i>风水知识
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="{{ shop_route('pages.show', 'about') }}">
              <i class="bi bi-info-circle me-1"></i>关于我们
            </a>
          </li>
        </ul>
        
        <ul class="navbar-nav">
          <!-- 搜索 -->
          <li class="nav-item me-3">
            <form class="d-flex" action="{{ shop_route('products.index') }}" method="GET">
              <div class="input-group">
                <input class="form-control form-control-mystical" type="search" name="keyword" placeholder="搜索水晶..." value="{{ request('keyword') }}">
                <button class="btn btn-mystical" type="submit">
                  <i class="bi bi-search"></i>
                </button>
              </div>
            </form>
          </li>
          
          <!-- 购物车 -->
          <li class="nav-item me-3">
            <a class="nav-link position-relative" href="{{ shop_route('carts.index') }}">
              <i class="bi bi-bag fs-5"></i>
              <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="cart-count">
                {{ cart_quantity() }}
              </span>
            </a>
          </li>
          
          <!-- 用户菜单 -->
          @if(current_customer())
            <li class="nav-item dropdown">
              <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                <i class="bi bi-person-circle me-1"></i>{{ current_customer()->name }}
              </a>
              <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="{{ shop_route('account.index') }}">个人中心</a></li>
                <li><a class="dropdown-item" href="{{ shop_route('account.orders.index') }}">我的订单</a></li>
                <li><a class="dropdown-item" href="{{ shop_route('account.addresses.index') }}">收货地址</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="{{ shop_route('logout') }}">退出登录</a></li>
              </ul>
            </li>
          @else
            <li class="nav-item">
              <a class="nav-link" href="{{ shop_route('login') }}">
                <i class="bi bi-box-arrow-in-right me-1"></i>登录
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ shop_route('register') }}">
                <i class="bi bi-person-plus me-1"></i>注册
              </a>
            </li>
          @endif
        </ul>
      </div>
    </div>
  </nav>

  <!-- 主要内容 -->
  <main class="main-content" style="margin-top: 80px; min-height: calc(100vh - 80px);">
    @yield('content')
  </main>

  <!-- 页脚 -->
  <footer class="bg-dark text-light py-5 mt-5">
    <div class="container">
      <div class="row">
        <div class="col-lg-4 mb-4">
          <h5 class="mystical-glow mb-3">
            <i class="bi bi-gem me-2"></i>玄晶阁
          </h5>
          <p class="text-muted">
            专注于高品质风水水晶饰品，传承千年玄学智慧，为您的生活带来正能量与好运。
          </p>
          <div class="d-flex gap-3">
            <a href="#" class="text-muted"><i class="bi bi-wechat fs-4"></i></a>
            <a href="#" class="text-muted"><i class="bi bi-qq fs-4"></i></a>
            <a href="#" class="text-muted"><i class="bi bi-telephone fs-4"></i></a>
          </div>
        </div>
        
        <div class="col-lg-2 col-md-6 mb-4">
          <h6 class="text-uppercase mb-3">商品分类</h6>
          <ul class="list-unstyled">
            <li><a href="#" class="text-muted text-decoration-none">招财水晶</a></li>
            <li><a href="#" class="text-muted text-decoration-none">辟邪水晶</a></li>
            <li><a href="#" class="text-muted text-decoration-none">增运水晶</a></li>
            <li><a href="#" class="text-muted text-decoration-none">健康水晶</a></li>
          </ul>
        </div>
        
        <div class="col-lg-2 col-md-6 mb-4">
          <h6 class="text-uppercase mb-3">客户服务</h6>
          <ul class="list-unstyled">
            <li><a href="#" class="text-muted text-decoration-none">购买指南</a></li>
            <li><a href="#" class="text-muted text-decoration-none">保养方法</a></li>
            <li><a href="#" class="text-muted text-decoration-none">退换政策</a></li>
            <li><a href="#" class="text-muted text-decoration-none">联系我们</a></li>
          </ul>
        </div>
        
        <div class="col-lg-4 mb-4">
          <h6 class="text-uppercase mb-3">联系信息</h6>
          <ul class="list-unstyled">
            <li class="mb-2">
              <i class="bi bi-geo-alt me-2"></i>
              <span class="text-muted">中国·北京·朝阳区</span>
            </li>
            <li class="mb-2">
              <i class="bi bi-telephone me-2"></i>
              <span class="text-muted">************</span>
            </li>
            <li class="mb-2">
              <i class="bi bi-envelope me-2"></i>
              <span class="text-muted"><EMAIL></span>
            </li>
            <li class="mb-2">
              <i class="bi bi-clock me-2"></i>
              <span class="text-muted">周一至周日 9:00-21:00</span>
            </li>
          </ul>
        </div>
      </div>
      
      <hr class="my-4">
      
      <div class="row align-items-center">
        <div class="col-md-6">
          <p class="text-muted mb-0">
            &copy; {{ date('Y') }} 玄晶阁. 保留所有权利.
          </p>
        </div>
        <div class="col-md-6 text-md-end">
          <p class="text-muted mb-0">
            <small>京ICP备12345678号 | 京公网安备11010502012345号</small>
          </p>
        </div>
      </div>
    </div>
  </footer>

  <!-- 返回顶部按钮 -->
  <button id="back-to-top" class="btn btn-mystical position-fixed bottom-0 end-0 m-4" style="display: none; z-index: 1000;">
    <i class="bi bi-arrow-up"></i>
  </button>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  
  <!-- jQuery -->
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  
  <!-- 自定义JS -->
  <script>
    $(document).ready(function() {
      // 页面加载完成后隐藏加载动画
      $('#page-loader').fadeOut(500);
      
      // 返回顶部按钮
      $(window).scroll(function() {
        if ($(this).scrollTop() > 300) {
          $('#back-to-top').fadeIn();
        } else {
          $('#back-to-top').fadeOut();
        }
      });
      
      $('#back-to-top').click(function() {
        $('html, body').animate({scrollTop: 0}, 600);
        return false;
      });
      
      // 购物车数量更新
      function updateCartCount() {
        $.get('{{ shop_route("carts.mini") }}', function(data) {
          $('#cart-count').text(data.quantity);
        });
      }
      
      // 添加到购物车
      $('.add-to-cart').click(function(e) {
        e.preventDefault();
        const productId = $(this).data('product-id');
        const quantity = $(this).data('quantity') || 1;
        
        $.post('{{ shop_route("carts.store") }}', {
          product_id: productId,
          quantity: quantity,
          _token: '{{ csrf_token() }}'
        }).done(function(data) {
          if (data.status === 'success') {
            updateCartCount();
            // 显示成功提示
            showToast('success', '商品已添加到购物车');
          } else {
            showToast('error', data.message || '添加失败');
          }
        }).fail(function() {
          showToast('error', '网络错误，请稍后重试');
        });
      });
      
      // 显示提示信息
      function showToast(type, message) {
        const toastHtml = `
          <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0" role="alert">
            <div class="d-flex">
              <div class="toast-body">${message}</div>
              <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
          </div>
        `;
        
        if (!$('#toast-container').length) {
          $('body').append('<div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3"></div>');
        }
        
        const $toast = $(toastHtml);
        $('#toast-container').append($toast);
        
        const toast = new bootstrap.Toast($toast[0]);
        toast.show();
        
        $toast.on('hidden.bs.toast', function() {
          $(this).remove();
        });
      }
    });
  </script>
  
  @stack('scripts')
</body>
</html>
