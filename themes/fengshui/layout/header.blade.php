@include('shared.menu-mobile')

<header class="header-wrap">
  @hook('header.before')

  <!-- 顶部信息栏 -->
  <div class="header-top">
    <div class="container">
      <div class="header-info">
        @if (system_setting('base.telephone', ''))
          <span class="info-item">
            <i class="fas fa-phone"></i>
            客服热线：{{ system_setting('base.telephone') }}
          </span>
        @endif
        <span class="info-item">
          <i class="fas fa-envelope"></i>
          邮箱：<EMAIL>
        </span>
        <span class="info-item">
          <i class="fas fa-clock"></i>
          营业时间：9:00-21:00
        </span>
      </div>
      <div class="header-actions">
        @hookwrapper('header.top.currency')
        @if (currencies()->count() > 1)
          <div class="dropdown">
            <a class="action-link dropdown-toggle" href="javascript:void(0)" role="button" id="currency-dropdown" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="fas fa-coins"></i>
              @foreach (currencies() as $currency)
                @if ($currency->code == current_currency_code())
                  {{ $currency->name }}
                @endif
              @endforeach
            </a>
            <div class="dropdown-menu" aria-labelledby="currency-dropdown">
              @foreach (currencies() as $currency)
                <a class="dropdown-item" href="{{ shop_route('currency.switch', [$currency->code]) }}">
                  @if ($currency->symbol_left){{ $currency->symbol_left }}@endif
                  {{ $currency->name }}
                  @if ($currency->symbol_right){{ $currency->symbol_right }}@endif
                </a>
              @endforeach
            </div>
          </div>
        @endif
        @endhookwrapper

        @hookwrapper('header.top.language')
        @if (count($languages) > 1)
          <div class="dropdown">
            <a class="action-link dropdown-toggle" href="javascript:void(0)" role="button" id="language-dropdown" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="fas fa-globe"></i>
              {{ current_language()->name }}
            </a>
            <div class="dropdown-menu" aria-labelledby="language-dropdown">
              @foreach ($languages as $language)
                <a class="dropdown-item" href="{{ shop_route('lang.switch', [$language->code]) }}">
                  {{ $language->name }}
                </a>
              @endforeach
            </div>
          </div>
        @endif
        @endhookwrapper

        @if(current_customer())
          <a href="{{ shop_route('account.index') }}" class="action-link">
            <i class="fas fa-user"></i>
            {{ current_customer()->name }}
          </a>
          <a href="{{ shop_route('logout') }}" class="action-link">
            <i class="fas fa-sign-out-alt"></i>
            退出
          </a>
        @else
          <a href="{{ shop_route('login.index') }}" class="action-link">
            <i class="fas fa-sign-in-alt"></i>
            登录
          </a>
          <a href="{{ shop_route('register.index') }}" class="action-link">
            <i class="fas fa-user-plus"></i>
            注册
          </a>
        @endif

        @hook('header.top.right')
      </div>
    </div>
  </div>

  <!-- 主导航栏 -->
  <div class="header-main">
    <div class="container">
      <nav class="navbar navbar-expand-lg">
        @hookwrapper('header.menu.logo')
        <a class="navbar-brand" href="{{ shop_route('home.index') }}">
          <div class="logo-icon mystical-glow">
            <i class="fas fa-gem"></i>
          </div>
          <span class="logo-text mystical-text">风水水晶</span>
        </a>
        @endhookwrapper

        <!-- 搜索栏 -->
        <div class="header-search d-none d-lg-block">
          <form class="search-form" action="{{ shop_route('products.search') }}" method="GET">
            <input type="text" name="keyword" class="search-input" placeholder="搜索水晶饰品..." value="{{ request('keyword') }}">
            <button type="submit" class="search-btn">
              <i class="fas fa-search"></i>
            </button>
          </form>
        </div>

        <!-- 用户操作区域 -->
        <div class="header-actions d-none d-lg-flex">
          @hookwrapper('header.menu.icon')
          <div class="action-item">
            <a href="{{ shop_route('account.wishlist.index') }}" class="action-link">
              <i class="fas fa-heart action-icon"></i>
              <span class="action-text">愿望清单</span>
              @if(current_customer())
                <span class="action-badge">{{ current_customer()->wishlists()->count() }}</span>
              @endif
            </a>
          </div>
          <div class="action-item">
            <a href="{{ shop_route('carts.index') }}" class="action-link cart-icon">
              <i class="fas fa-shopping-cart action-icon"></i>
              <span class="action-text">购物车</span>
              <span class="action-badge cart-count">{{ current_customer() ? current_customer()->carts()->sum('quantity') : 0 }}</span>
            </a>
          </div>
          @endhookwrapper
        </div>

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
          <span class="navbar-toggler-icon">
            <i class="fas fa-bars" style="color: var(--fengshui-accent);"></i>
          </span>
        </button>

        <div class="collapse navbar-collapse" id="navbarSupportedContent">
          <!-- 移动端搜索 -->
          <div class="header-search d-lg-none">
            <form class="search-form" action="{{ shop_route('products.search') }}" method="GET">
              <input type="text" name="keyword" class="search-input" placeholder="搜索水晶饰品..." value="{{ request('keyword') }}">
              <button type="submit" class="search-btn">
                <i class="fas fa-search"></i>
              </button>
            </form>
          </div>

          <div class="main-nav">
            @include('shared.menu-pc')
          </div>

          <!-- 移动端用户操作 -->
          <div class="header-actions d-lg-none">
            <div class="action-item">
              <a href="{{ shop_route('account.wishlist.index') }}" class="action-link">
                <i class="fas fa-heart action-icon"></i>
                <span class="action-text">愿望清单</span>
              </a>
            </div>
            <div class="action-item">
              <a href="{{ shop_route('carts.index') }}" class="action-link">
                <i class="fas fa-shopping-cart action-icon"></i>
                <span class="action-text">购物车</span>
              </a>
            </div>
          </div>
        </div>
      </nav>
    </div>
  </div>

  <!-- 分类导航栏 -->
  <div class="category-nav d-none d-lg-block">
    <div class="container">
      <div class="category-list">
        <a href="{{ shop_route('categories.show', ['category' => 100001]) }}" class="category-link">招财水晶</a>
        <a href="{{ shop_route('categories.show', ['category' => 100002]) }}" class="category-link">辟邪水晶</a>
        <a href="{{ shop_route('categories.show', ['category' => 100003]) }}" class="category-link">爱情水晶</a>
        <a href="{{ shop_route('categories.show', ['category' => 100004]) }}" class="category-link">健康水晶</a>
        <a href="{{ shop_route('categories.show', ['category' => 100005]) }}" class="category-link">智慧水晶</a>
        <a href="{{ shop_route('categories.show', ['category' => 300001]) }}" class="category-link">水晶手链</a>
        <a href="{{ shop_route('categories.show', ['category' => 300002]) }}" class="category-link">水晶项链</a>
        <a href="{{ shop_route('categories.show', ['category' => 300003]) }}" class="category-link">水晶摆件</a>
      </div>
    </div>
  </div>

  @hook('header.after')
</header>

<!-- 返回顶部按钮 -->
<button class="back-to-top" id="backToTop">
  <i class="fas fa-arrow-up"></i>
</button>
