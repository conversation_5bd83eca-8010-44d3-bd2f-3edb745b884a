<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CleanAndResetDataSeeder extends Seeder
{
    /**
     * 清理旧数据并重新生成纯净的风水数据
     */
    public function run()
    {
        $this->command->info('🧹 开始清理旧数据...');
        
        // 1. 清理所有产品相关数据
        $this->cleanProductData();
        
        // 2. 清理所有分类相关数据
        $this->cleanCategoryData();
        
        // 3. 清理所有文章相关数据
        $this->cleanPageData();
        
        $this->command->info('✨ 数据清理完成！');
        
        // 4. 重新生成纯净的风水数据
        $this->command->info('🔮 开始生成纯净的风水数据...');
        $this->call(FengshuiDataSeeder::class);
        
        $this->command->info('🎉 风水数据重新生成完成！');
    }

    /**
     * 清理产品相关数据
     */
    private function cleanProductData()
    {
        $this->command->info('清理产品数据...');
        
        // 清理产品分类关联
        DB::table('product_categories')->truncate();
        
        // 清理产品SKU
        DB::table('product_skus')->truncate();
        
        // 清理产品描述
        DB::table('product_descriptions')->truncate();
        
        // 清理产品主表
        DB::table('products')->truncate();
        
        $this->command->info('✅ 产品数据清理完成');
    }

    /**
     * 清理分类相关数据
     */
    private function cleanCategoryData()
    {
        $this->command->info('清理分类数据...');
        
        // 清理分类描述
        DB::table('category_descriptions')->truncate();
        
        // 清理分类主表
        DB::table('categories')->truncate();
        
        $this->command->info('✅ 分类数据清理完成');
    }

    /**
     * 清理文章相关数据
     */
    private function cleanPageData()
    {
        $this->command->info('清理文章数据...');
        
        // 清理文章描述
        DB::table('page_descriptions')->truncate();
        
        // 清理文章主表
        DB::table('pages')->truncate();
        
        $this->command->info('✅ 文章数据清理完成');
    }
}
