<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Beike\Models\Category;
use Beike\Models\Product;
use Beike\Models\ProductDescription;
use Beike\Models\ProductSku;
use Beike\Models\Page;
use Beike\Models\PageDescription;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class FengshuiDataSeeder extends Seeder
{
    /**
     * 风水水晶产品数据生成器
     */
    public function run()
    {
        $this->command->info('🔮 开始生成风水水晶数据...');

        // 1. 创建风水分类
        $this->createFengshuiCategories();

        // 2. 生成200个水晶产品
        $this->createCrystalProducts();

        // 3. 创建200篇风水文章
        $this->createFengshuiArticles();

        $this->command->info('✨ 风水水晶数据生成完成！');
    }

    /**
     * 创建风水分类体系
     */
    private function createFengshuiCategories()
    {
        $this->command->info('📂 创建风水分类...');

        // 按功效分类
        $effectCategories = [
            ['id' => 100001, 'name' => '招财水晶', 'description' => '招财进宝，财源广进的神奇水晶'],
            ['id' => 100002, 'name' => '辟邪水晶', 'description' => '驱邪避凶，护身保平安的守护水晶'],
            ['id' => 100003, 'name' => '爱情水晶', 'description' => '增进感情，招桃花的浪漫水晶'],
            ['id' => 100004, 'name' => '健康水晶', 'description' => '强身健体，祛病延年的养生水晶'],
            ['id' => 100005, 'name' => '智慧水晶', 'description' => '开启智慧，增强学业运的灵性水晶'],
            ['id' => 100006, 'name' => '事业水晶', 'description' => '助力事业，提升官运的成功水晶'],
        ];

        // 按水晶类型分类
        $typeCategories = [
            ['id' => 200001, 'name' => '紫水晶', 'description' => '高贵神秘的紫色水晶，智慧与灵性的象征'],
            ['id' => 200002, 'name' => '黄水晶', 'description' => '金黄璀璨的财富水晶，招财的首选'],
            ['id' => 200003, 'name' => '粉水晶', 'description' => '温柔粉嫩的爱情水晶，桃花运的守护者'],
            ['id' => 200004, 'name' => '白水晶', 'description' => '纯净透明的万能水晶，净化能量场'],
            ['id' => 200005, 'name' => '黑曜石', 'description' => '深邃神秘的保护石，强力辟邪'],
            ['id' => 200006, 'name' => '绿幽灵', 'description' => '翠绿通透的事业石，助力财运事业'],
            ['id' => 200007, 'name' => '红玛瑙', 'description' => '热情如火的能量石，增强活力'],
            ['id' => 200008, 'name' => '青金石', 'description' => '深蓝高贵的智慧石，开启第三眼'],
        ];

        // 按用途分类
        $usageCategories = [
            ['id' => 300001, 'name' => '水晶手链', 'description' => '贴身佩戴的水晶手链，时刻守护您的能量场'],
            ['id' => 300002, 'name' => '水晶项链', 'description' => '优雅美丽的水晶项链，提升个人魅力'],
            ['id' => 300003, 'name' => '水晶摆件', 'description' => '家居办公的水晶摆件，改善风水气场'],
            ['id' => 300004, 'name' => '水晶原石', 'description' => '天然未加工的水晶原石，原始能量最强'],
            ['id' => 300005, 'name' => '水晶球', 'description' => '完美球形的水晶球，聚集天地灵气'],
            ['id' => 300006, 'name' => '水晶吊坠', 'description' => '精美的水晶吊坠，贴心守护'],
        ];

        $allCategories = array_merge($effectCategories, $typeCategories, $usageCategories);

        foreach ($allCategories as $categoryData) {
            $category = Category::updateOrCreate(
                ['id' => $categoryData['id']],
                [
                    'parent_id' => 0,
                    'image' => '',
                    'position' => $categoryData['id'],
                    'active' => 1,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            );

            // 创建分类描述
            DB::table('category_descriptions')->updateOrInsert(
                ['category_id' => $category->id, 'locale' => 'en'],
                [
                    'name' => $categoryData['name'],
                    'content' => $categoryData['description'],
                    'meta_title' => $categoryData['name'] . ' - 风水水晶专卖',
                    'meta_description' => $categoryData['description'],
                    'meta_keywords' => $categoryData['name'] . ',风水,水晶,玄学',
                ]
            );
        }

        $this->command->info('✅ 风水分类创建完成');
    }

    /**
     * 生成200个水晶产品
     */
    private function createCrystalProducts()
    {
        $this->command->info('💎 生成200个水晶产品...');

        // 水晶产品基础数据
        $crystalTypes = [
            '紫水晶' => ['color' => '紫色', 'effect' => '智慧、灵性、冥想'],
            '黄水晶' => ['color' => '金黄色', 'effect' => '招财、财运、商业成功'],
            '粉水晶' => ['color' => '粉色', 'effect' => '爱情、人际关系、温柔'],
            '白水晶' => ['color' => '透明', 'effect' => '净化、平衡、万能'],
            '黑曜石' => ['color' => '黑色', 'effect' => '辟邪、保护、驱负能量'],
            '绿幽灵' => ['color' => '绿色', 'effect' => '事业、财运、成长'],
            '红玛瑙' => ['color' => '红色', 'effect' => '活力、勇气、热情'],
            '青金石' => ['color' => '深蓝色', 'effect' => '智慧、真理、沟通'],
            '月光石' => ['color' => '乳白色', 'effect' => '直觉、女性能量、情感平衡'],
            '虎眼石' => ['color' => '金棕色', 'effect' => '勇气、决断力、财富'],
        ];

        $productTypes = ['手链', '项链', '摆件', '原石', '球', '吊坠', '戒指', '耳环'];
        $sizes = ['小号', '中号', '大号', '特大号'];
        $grades = ['A级', 'AA级', 'AAA级', '收藏级'];

        // 水晶类型英文映射
        $crystalTypeMap = [
            '紫水晶' => 'AMETHYST',
            '黄水晶' => 'CITRINE',
            '粉水晶' => 'ROSE_QUARTZ',
            '白水晶' => 'CLEAR_QUARTZ',
            '黑曜石' => 'OBSIDIAN',
            '绿幽灵' => 'GREEN_PHANTOM',
            '红玛瑙' => 'RED_AGATE',
            '青金石' => 'LAPIS_LAZULI',
            '月光石' => 'MOONSTONE',
            '虎眼石' => 'TIGER_EYE',
        ];

        for ($i = 1; $i <= 200; $i++) {
            $crystalType = array_rand($crystalTypes);
            $productType = $productTypes[array_rand($productTypes)];
            $size = $sizes[array_rand($sizes)];
            $grade = $grades[array_rand($grades)];

            $productName = $crystalType . $productType . ' ' . $size . ' ' . $grade;
            $crystalTypeEn = $crystalTypeMap[$crystalType] ?? 'CRYSTAL';

            // 创建产品
            $product = Product::create([
                'brand_id' => 1,
                'images' => json_encode(['catalog/demo/product/' . (($i % 10) + 1) . '.jpg']),
                'price' => rand(88, 9999),
                'video' => '',
                'position' => $i,
                'shipping' => 1,
                'active' => 1,
                'variables' => json_encode([]),
                'tax_class_id' => 0,
                'weight' => rand(10, 500) / 10,
                'weight_class' => 'kg',
                'sales' => rand(0, 100),
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // 创建产品描述
            ProductDescription::create([
                'product_id' => $product->id,
                'locale' => 'en',
                'name' => $productName,
                'content' => $this->generateProductDescription($crystalType, $productType, $crystalTypes[$crystalType]),
                'meta_title' => $productName . ' - 正宗天然水晶',
                'meta_description' => '天然' . $productName . '，' . $crystalTypes[$crystalType]['effect'] . '，正品保证，开光加持',
                'meta_keywords' => $crystalType . ',' . $productType . ',水晶,风水,玄学,天然',
            ]);

            // 创建产品SKU
            ProductSku::create([
                'product_id' => $product->id,
                'variants' => json_encode([]),
                'position' => 1,
                'images' => json_encode([]),
                'model' => 'FS' . str_pad($i, 6, '0', STR_PAD_LEFT),
                'sku' => 'CRYSTAL-' . $crystalTypeEn . '-' . str_pad($i, 3, '0', STR_PAD_LEFT),
                'price' => $product->price,
                'origin_price' => $product->price + rand(100, 500),
                'cost_price' => $product->price * 0.6,
                'quantity' => rand(10, 100),
                'is_default' => 1,
            ]);

            // 关联分类
            $this->assignProductToCategories($product->id, $crystalType, $productType);

            if ($i % 20 == 0) {
                $this->command->info("已生成 {$i} 个产品...");
            }
        }

        $this->command->info('✅ 200个水晶产品生成完成');
    }

    /**
     * 生成产品详细描述
     */
    private function generateProductDescription($crystalType, $productType, $crystalInfo)
    {
        $templates = [
            "这款{$crystalType}{$productType}采用天然优质{$crystalType}精心打造，{$crystalInfo['color']}光泽温润，能量纯净。具有{$crystalInfo['effect']}的神奇功效，是您修身养性、改善运势的理想选择。",

            "精选天然{$crystalType}制作的{$productType}，色泽{$crystalInfo['color']}，晶体通透。在风水学中，{$crystalType}具有{$crystalInfo['effect']}的强大能量，长期佩戴可以显著改善个人气场。",

            "这件{$crystalType}{$productType}经过专业师傅精心挑选和加工，保证每一件都是上乘佳品。{$crystalType}的{$crystalInfo['effect']}功效已被广泛认可，是现代人提升运势的必备法器。",
        ];

        $description = $templates[array_rand($templates)];

        $description .= "\n\n【产品特色】\n";
        $description .= "• 100%天然{$crystalType}，无任何人工处理\n";
        $description .= "• 专业开光加持，能量更加纯净\n";
        $description .= "• 精美包装，送礼自用两相宜\n";
        $description .= "• 提供权威鉴定证书\n\n";

        $description .= "【使用方法】\n";
        if ($productType == '手链' || $productType == '项链' || $productType == '吊坠') {
            $description .= "建议贴身佩戴，让水晶与您的能量场产生共振。初次佩戴前请先净化水晶，可用清水冲洗或月光照射。\n\n";
        } else {
            $description .= "建议摆放在客厅、卧室或办公室等重要位置，根据风水原理选择最佳摆放方位。定期清洁和净化，保持水晶能量活跃。\n\n";
        }

        $description .= "【注意事项】\n";
        $description .= "• 避免与化学物品接触\n";
        $description .= "• 定期净化保养\n";
        $description .= "• 心诚则灵，保持正念\n";

        return $description;
    }

    /**
     * 为产品分配分类
     */
    private function assignProductToCategories($productId, $crystalType, $productType)
    {
        $categoryMappings = [
            // 按水晶类型
            '紫水晶' => 200001,
            '黄水晶' => 200002,
            '粉水晶' => 200003,
            '白水晶' => 200004,
            '黑曜石' => 200005,
            '绿幽灵' => 200006,
            '红玛瑙' => 200007,
            '青金石' => 200008,

            // 按产品类型
            '手链' => 300001,
            '项链' => 300002,
            '摆件' => 300003,
            '原石' => 300004,
            '球' => 300005,
            '吊坠' => 300006,
        ];

        $categories = [];

        // 添加水晶类型分类
        if (isset($categoryMappings[$crystalType])) {
            $categories[] = $categoryMappings[$crystalType];
        }

        // 添加产品类型分类
        if (isset($categoryMappings[$productType])) {
            $categories[] = $categoryMappings[$productType];
        }

        // 根据水晶类型添加功效分类
        $effectMappings = [
            '黄水晶' => 100001, // 招财
            '绿幽灵' => 100001, // 招财
            '黑曜石' => 100002, // 辟邪
            '粉水晶' => 100003, // 爱情
            '红玛瑙' => 100004, // 健康
            '紫水晶' => 100005, // 智慧
            '青金石' => 100005, // 智慧
            '虎眼石' => 100006, // 事业
        ];

        if (isset($effectMappings[$crystalType])) {
            $categories[] = $effectMappings[$crystalType];
        }

        // 插入产品分类关联
        foreach (array_unique($categories) as $categoryId) {
            DB::table('product_categories')->insert([
                'product_id' => $productId,
                'category_id' => $categoryId,
            ]);
        }
    }

    /**
     * 创建200篇风水文章
     */
    private function createFengshuiArticles()
    {
        $this->command->info('📝 生成200篇风水文章...');

        $articleCategories = [
            '水晶知识' => [
                '水晶的形成原理与能量来源',
                '如何鉴别天然水晶与人造水晶',
                '水晶的净化与保养方法',
                '不同颜色水晶的能量特性',
                '水晶与脉轮的对应关系',
            ],
            '风水布局' => [
                '家居风水中的水晶摆放技巧',
                '办公室风水布局与水晶应用',
                '卧室风水禁忌与化解方法',
                '客厅财位的确定与布置',
                '玄关风水的重要性与布局',
            ],
            '运势提升' => [
                '如何利用水晶提升财运',
                '增强桃花运的风水秘诀',
                '事业运势的风水调理方法',
                '学业运势的提升技巧',
                '健康运势的风水调节',
            ],
            '玄学文化' => [
                '中国古代风水学的发展历程',
                '五行理论在现代生活中的应用',
                '八卦方位与家居布局的关系',
                '生肖与水晶的搭配原理',
                '星座与水晶能量的对应关系',
            ],
            '实用指南' => [
                '新手如何选择第一块水晶',
                '水晶佩戴的注意事项',
                '水晶摆件的摆放原则',
                '如何为水晶开光加持',
                '水晶能量的感知与运用',
            ],
        ];

        $articleId = 1;
        foreach ($articleCategories as $category => $titles) {
            foreach ($titles as $title) {
                for ($i = 1; $i <= 8; $i++) { // 每个标题生成8篇文章
                    $fullTitle = $title . '（' . ['基础篇', '进阶篇', '实战篇', '案例篇', '深度篇', '专家篇', '秘传篇', '大师篇'][$i-1] . '）';

                    $page = Page::create([
                        'page_category_id' => 1,
                        'image' => '',
                        'position' => $articleId,
                        'views' => rand(100, 5000),
                        'author' => '风水大师',
                        'active' => 1,
                        'created_at' => now()->subDays(rand(1, 365)),
                        'updated_at' => now(),
                    ]);

                    PageDescription::create([
                        'page_id' => $page->id,
                        'locale' => 'en',
                        'title' => $fullTitle,
                        'summary' => '本文详细介绍了' . $title . '的相关知识，包含理论基础、实践方法和注意事项',
                        'content' => $this->generateArticleContent($category, $title, $i),
                        'meta_title' => $fullTitle . ' - 风水玄学知识大全',
                        'meta_description' => '专业的' . $category . '知识分享，' . $title . '的详细解析，助您掌握风水玄学精髓',
                        'meta_keywords' => $category . ',' . $title . ',风水,玄学,水晶,运势',
                    ]);

                    $articleId++;
                    if ($articleId > 200) break 2;
                }
                if ($articleId > 200) break;
            }
            if ($articleId > 200) break;
        }

        $this->command->info('✅ 200篇风水文章生成完成');
    }

    /**
     * 生成文章内容
     */
    private function generateArticleContent($category, $title, $level)
    {
        $levelNames = ['基础篇', '进阶篇', '实战篇', '案例篇', '深度篇', '专家篇', '秘传篇', '大师篇'];
        $levelName = $levelNames[$level - 1];

        $content = "<h2>{$title}（{$levelName}）</h2>\n\n";

        // 根据分类生成不同的内容模板
        switch ($category) {
            case '水晶知识':
                $content .= $this->generateCrystalKnowledgeContent($title, $level);
                break;
            case '风水布局':
                $content .= $this->generateFengshuiLayoutContent($title, $level);
                break;
            case '运势提升':
                $content .= $this->generateLuckEnhancementContent($title, $level);
                break;
            case '玄学文化':
                $content .= $this->generateMysticCultureContent($title, $level);
                break;
            case '实用指南':
                $content .= $this->generatePracticalGuideContent($title, $level);
                break;
        }

        // 添加通用结尾
        $content .= "\n\n<h3>总结</h3>\n";
        $content .= "<p>通过本文的学习，相信您对{$title}有了更深入的了解。风水玄学博大精深，需要我们用心体悟，持续学习。在实践中不断积累经验，才能真正掌握其中的奥秘。</p>\n\n";

        $content .= "<p><strong>温馨提示：</strong>风水调理需要因人而异，建议在专业人士指导下进行。如有疑问，欢迎咨询我们的风水大师。</p>\n\n";

        $content .= "<div class='article-tags'>\n";
        $content .= "<span class='tag'>#{$category}</span>\n";
        $content .= "<span class='tag'>#{$title}</span>\n";
        $content .= "<span class='tag'>#风水玄学</span>\n";
        $content .= "<span class='tag'>#水晶能量</span>\n";
        $content .= "</div>";

        return $content;
    }

    /**
     * 生成水晶知识内容
     */
    private function generateCrystalKnowledgeContent($title, $level)
    {
        $content = "<p>水晶作为大自然的瑰宝，蕴含着强大的能量场。在风水学中，水晶被誉为'地球的眼泪'，具有净化、平衡和增强能量的神奇功效。</p>\n\n";

        $content .= "<h3>水晶的基本特性</h3>\n";
        $content .= "<ul>\n";
        $content .= "<li><strong>能量振动：</strong>每种水晶都有其独特的振动频率，能够与人体能量场产生共振</li>\n";
        $content .= "<li><strong>色彩能量：</strong>不同颜色的水晶对应不同的脉轮和功效</li>\n";
        $content .= "<li><strong>形状影响：</strong>水晶的形状会影响其能量的传导方式</li>\n";
        $content .= "<li><strong>纯净度：</strong>水晶的纯净度直接影响其能量强度</li>\n";
        $content .= "</ul>\n\n";

        if ($level >= 3) {
            $content .= "<h3>高级应用技巧</h3>\n";
            $content .= "<p>对于有一定基础的朋友，可以尝试以下高级技巧：</p>\n";
            $content .= "<ol>\n";
            $content .= "<li>水晶阵法的布置与激活</li>\n";
            $content .= "<li>多种水晶的组合使用</li>\n";
            $content .= "<li>水晶与其他风水物品的搭配</li>\n";
            $content .= "<li>根据个人八字选择适合的水晶</li>\n";
            $content .= "</ol>\n\n";
        }

        return $content;
    }

    /**
     * 生成风水布局内容
     */
    private function generateFengshuiLayoutContent($title, $level)
    {
        $content = "<p>风水布局是一门古老而深奥的学问，通过合理的空间布置和物品摆放，可以有效改善居住环境的能量场，为居住者带来好运。</p>\n\n";

        $content .= "<h3>基本布局原则</h3>\n";
        $content .= "<ul>\n";
        $content .= "<li><strong>藏风聚气：</strong>营造良好的气场循环</li>\n";
        $content .= "<li><strong>阴阳平衡：</strong>保持空间的阴阳协调</li>\n";
        $content .= "<li><strong>五行相生：</strong>利用五行相生的原理</li>\n";
        $content .= "<li><strong>方位正确：</strong>根据八卦方位进行布置</li>\n";
        $content .= "</ul>\n\n";

        $content .= "<h3>实用布局技巧</h3>\n";
        $content .= "<p>在实际布局中，需要注意以下几个要点：</p>\n";
        $content .= "<ol>\n";
        $content .= "<li>入户门的朝向与玄关设计</li>\n";
        $content .= "<li>客厅沙发与茶几的摆放</li>\n";
        $content .= "<li>卧室床位的选择与布置</li>\n";
        $content .= "<li>厨房与卫生间的风水处理</li>\n";
        $content .= "</ol>\n\n";

        return $content;
    }

    /**
     * 生成运势提升内容
     */
    private function generateLuckEnhancementContent($title, $level)
    {
        $content = "<p>运势的好坏往往影响着我们生活的方方面面。通过合理的风水调理和水晶能量的运用，可以有效提升个人运势，改善生活质量。</p>\n\n";

        $content .= "<h3>运势提升的基本方法</h3>\n";
        $content .= "<ul>\n";
        $content .= "<li><strong>环境调理：</strong>改善居住和工作环境的风水</li>\n";
        $content .= "<li><strong>能量补充：</strong>通过水晶等物品补充正能量</li>\n";
        $content .= "<li><strong>心态调整：</strong>保持积极乐观的心态</li>\n";
        $content .= "<li><strong>行为改善：</strong>调整日常行为习惯</li>\n";
        $content .= "</ul>\n\n";

        $content .= "<h3>具体实施步骤</h3>\n";
        $content .= "<p>运势提升需要循序渐进，建议按以下步骤进行：</p>\n";
        $content .= "<ol>\n";
        $content .= "<li>分析当前运势状况</li>\n";
        $content .= "<li>确定需要改善的方面</li>\n";
        $content .= "<li>制定具体的调理方案</li>\n";
        $content .= "<li>逐步实施并观察效果</li>\n";
        $content .= "</ol>\n\n";

        return $content;
    }

    /**
     * 生成玄学文化内容
     */
    private function generateMysticCultureContent($title, $level)
    {
        $content = "<p>中华玄学文化源远流长，包含了丰富的哲学思想和实践智慧。了解这些传统文化，有助于我们更好地理解和运用风水学的精髓。</p>\n\n";

        $content .= "<h3>传统文化基础</h3>\n";
        $content .= "<ul>\n";
        $content .= "<li><strong>易经八卦：</strong>万物变化的根本规律</li>\n";
        $content .= "<li><strong>五行学说：</strong>宇宙万物的基本构成</li>\n";
        $content .= "<li><strong>阴阳理论：</strong>对立统一的哲学思想</li>\n";
        $content .= "<li><strong>天人合一：</strong>人与自然和谐共处</li>\n";
        $content .= "</ul>\n\n";

        $content .= "<h3>现代应用价值</h3>\n";
        $content .= "<p>古老的玄学智慧在现代生活中仍有重要价值：</p>\n";
        $content .= "<ol>\n";
        $content .= "<li>指导我们建立正确的世界观</li>\n";
        $content .= "<li>帮助我们理解事物发展规律</li>\n";
        $content .= "<li>提供解决问题的思路方法</li>\n";
        $content .= "<li>促进身心健康和谐发展</li>\n";
        $content .= "</ol>\n\n";

        return $content;
    }

    /**
     * 生成实用指南内容
     */
    private function generatePracticalGuideContent($title, $level)
    {
        $content = "<p>实践是检验真理的唯一标准。在学习风水玄学的过程中，理论与实践相结合才能真正掌握其精髓。本指南将为您提供详细的实操方法。</p>\n\n";

        $content .= "<h3>实践要点</h3>\n";
        $content .= "<ul>\n";
        $content .= "<li><strong>循序渐进：</strong>从简单的方法开始学习</li>\n";
        $content .= "<li><strong>持之以恒：</strong>坚持长期实践才能见效</li>\n";
        $content .= "<li><strong>因地制宜：</strong>根据具体情况灵活调整</li>\n";
        $content .= "<li><strong>心诚则灵：</strong>保持虔诚的心态</li>\n";
        $content .= "</ul>\n\n";

        $content .= "<h3>常见问题解答</h3>\n";
        $content .= "<p>在实践过程中，经常会遇到以下问题：</p>\n";
        $content .= "<ol>\n";
        $content .= "<li><strong>Q：</strong>多长时间能看到效果？<br><strong>A：</strong>一般需要1-3个月的时间，具体因人而异。</li>\n";
        $content .= "<li><strong>Q：</strong>是否需要专业指导？<br><strong>A：</strong>建议初学者寻求专业人士的指导。</li>\n";
        $content .= "<li><strong>Q：</strong>如何判断方法是否有效？<br><strong>A：</strong>通过观察生活中的变化来判断。</li>\n";
        $content .= "</ol>\n\n";

        return $content;
    }
}
