<?php

/**
 * 风水玄学水晶饰品销售主题设置脚本
 * 实现高端、优雅、神秘的UI主题风格
 */

define('IN_BEIKE', true);
require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

// 加载Laravel应用
$app = require_once __DIR__ . '/bootstrap/app.php';

// 绑定重要接口到容器
$app->singleton(
    Illuminate\Contracts\Http\Kernel::class,
    App\Http\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Console\Kernel::class,
    App\Console\Kernel::class
);

$app->singleton(
    Illuminate\Contracts\Debug\ExceptionHandler::class,
    App\Exceptions\Handler::class
);

// 启动应用
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$response = $kernel->handle(
    $request = Illuminate\Http\Request::capture()
);

echo "开始设置风水玄学水晶饰品销售主题...\n";

// 1. 更新系统设置 - 网站基本信息
$settings = [
    // 设置主题配置
    [
        'type' => 'theme',
        'space' => 'base',
        'name' => 'meta_title',
        'value' => '灵玉轩 - 风水玄学水晶饰品专卖',
        'json' => false,
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'type' => 'theme',
        'space' => 'base',
        'name' => 'meta_description',
        'value' => '灵玉轩专业销售各类风水水晶、开运饰品，助您改善家居风水，提升个人运势。',
        'json' => false,
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'type' => 'theme',
        'space' => 'base',
        'name' => 'meta_keywords',
        'value' => '风水水晶,开运饰品,能量石,水晶手链,家居风水,招财进宝,镇宅化煞',
        'json' => false,
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'type' => 'theme_fengshui',
        'space' => 'theme',
        'name' => 'primary_color',
        'value' => '#8b5a2b',
        'json' => false,
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'type' => 'theme_fengshui',
        'space' => 'theme',
        'name' => 'secondary_color',
        'value' => '#4a3520',
        'json' => false,
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'type' => 'theme_fengshui',
        'space' => 'theme',
        'name' => 'accent_color',
        'value' => '#d4af37',
        'json' => false,
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'type' => 'theme_fengshui',
        'space' => 'theme',
        'name' => 'logo',
        'value' => 'themes/fengshui/assets/images/logo.png',
        'json' => false,
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'type' => 'theme_fengshui',
        'space' => 'theme',
        'name' => 'favicon',
        'value' => 'themes/fengshui/assets/images/favicon.ico',
        'json' => false,
        'created_at' => now(),
        'updated_at' => now(),
    ],
    [
        'type' => 'theme_fengshui',
        'space' => 'theme',
        'name' => 'homepage_style',
        'value' => 'fengshui',
        'json' => false,
        'created_at' => now(),
        'updated_at' => now(),
    ],
];

foreach ($settings as $setting) {
    $exists = DB::table('settings')
        ->where('type', $setting['type'])
        ->where('space', $setting['space'])
        ->where('name', $setting['name'])
        ->exists();
        
    if ($exists) {
        DB::table('settings')
            ->where('type', $setting['type'])
            ->where('space', $setting['space'])
            ->where('name', $setting['name'])
            ->update([
                'value' => $setting['value'],
                'updated_at' => now(),
            ]);
    } else {
        DB::table('settings')->insert($setting);
    }
}

echo "风水玄学水晶饰品销售主题设置完成！\n";
echo "请访问网站前台查看效果，若需要进一步修改，可以在管理后台 -> 设置 -> 系统 中调整。\n";
